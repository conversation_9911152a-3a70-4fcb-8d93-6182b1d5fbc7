# ReviewPulse Monorepo - Global .gitignore

# ============================================
# ENVIRONMENT & SECRETS
# ============================================
.env
.env.*
!.env.example
*.pem
secrets/

# ============================================
# NODE.JS & FRONTEND (Next.js)
# ============================================
# Dependencies
node_modules/
**/node_modules/
.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# Build outputs
.next/
out/
build/
dist/

# Testing
coverage/

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# TypeScript
*.tsbuildinfo
next-env.d.ts

# Vercel deployment
.vercel

# ============================================
# PYTHON & BACKEND
# ============================================
# Python cache
__pycache__/
*.py[cod]
*$py.class
*.so
.Python

# Virtual environments
env/
venv/
ENV/
env.bak/
venv.bak/
.venv/

# Python testing & coverage
.pytest_cache/
.coverage
htmlcov/
.tox/

# Python distribution
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# ============================================
# DATABASE & DATA
# ============================================
*.db
*.sqlite
*.sqlite3
database.db
backups/

# ============================================
# IDE & EDITOR
# ============================================
.vscode/
.idea/
*.swp
*.swo
*~

# ============================================
# OS GENERATED
# ============================================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ============================================
# LOGS & MONITORING
# ============================================
*.log
logs/
*.log.*

# ============================================
# TEMPORARY & CACHE
# ============================================
tmp/
temp/
.cache/

# ============================================
# MISC
# ============================================
*.bak
*.tmp
*.temp