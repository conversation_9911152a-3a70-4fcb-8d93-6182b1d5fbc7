# Product Requirements Document (PRD) — ReviewPulse MVP

## 1. Overview
**ReviewPulse** is a SaaS tool for SMEs that aggregates Google Reviews for a given business, applies AI-powered sentiment analysis, and generates actionable weekly/monthly summaries.  
The MVP will focus solely on **Google Reviews aggregation**, **sentiment analysis**, and **email report delivery**.

---

## 2. Goals
- Centralize customer feedback from Google Reviews in one place.
- Automatically analyze sentiment for a given period.
- Generate clear, actionable summaries of positive and negative feedback.
- Send results via email and display them in a web dashboard.

---

## 3. Users
- **Primary**: Small–medium business owners (restaurants, salons, retail, services).
- **Secondary**: Marketing managers for SMEs.

---

## 4. Scope — MVP

### In Scope
- Supabase Auth for user login/registration.
- Supabase Database for storing reviews, sentiment results, and summaries.
- Google Reviews fetching via Google Places API.
- AI sentiment analysis using Azure Cognitive Services or OpenAI.
- AI-generated summaries (positive trends, negative trends, action items).
- Email report delivery (weekly or monthly).
- Simple dashboard with:
  - Review list view
  - Sentiment chart
  - Summary section
  - Date range filter

### Out of Scope (Future Phases)
- Instagram, App Store, competitor tracking, dark social integrations.
- Employee performance tracking.
- Multi-location support.
- Maps-based competitor dashboard.

---

## 5. Functional Requirements

### 1. Authentication
- Supabase Auth with email/password and social logins.

### 2. Review Fetching
- Input: Google Place ID or business name + location.
- Fetch reviews for a user-selected period.
- Store raw review text, rating, date, and author (if available).

### 3. Sentiment Analysis
- Classify each review as Positive, Neutral, or Negative.
- Store sentiment results alongside reviews.

### 4. AI Summarization
- Weekly/monthly summary with:
  - Positive themes.
  - Negative themes.
  - Recommended improvements.

### 5. Email Reports
- Generate a nicely formatted HTML email with:
  - Sentiment distribution chart.
  - Top positive & negative themes.
  - Link to dashboard for full details.
- Provider: **Resend** (strong docs, developer-friendly, API-first).

### 6. Dashboard
- Review feed with sentiment badges.
- Sentiment chart (line/bar chart over time).
- AI summary box.
- Date range selector.

---

## 6. Non-Functional Requirements
- Handle at least 5k reviews without noticeable performance degradation.
- Secure API routes with Supabase JWT-based auth.
- Ensure GDPR compliance for EU users (no unnecessary data retention).
- Use API rate limiting to prevent Google API bans.

---

## 7. Tech Stack

| Layer         | Technology |
|---------------|------------|
| **Frontend**  | Next.js + shadcn UI (UI components) |
| **Auth**      | Supabase Auth |
| **Database**  | Supabase PostgreSQL |
| **Backend**   | Python + FastAPI (deployed on Render/Azure) |
| **AI Services** | Azure Cognitive Services (Text Analytics) OR OpenAI GPT-4o |
| **Scraping/API** | Google Places API (primary), Playwright fallback for missing data |
| **Email Provider** | Resend (developer-first email API) |
| **Hosting**   | Vercel (frontend), Render/Azure App Service (backend) |

---

## 8. Architecture

### High-Level Flow
1. **User Registration/Login**
   - User signs up via Supabase Auth.
   - JWT token used for secure API calls.

2. **Review Aggregation**
   - User enters Google Place ID (or business name).
   - Backend calls Google Places API to fetch reviews for the selected time range.
   - Reviews stored in Supabase DB.

3. **AI Sentiment Analysis**
   - Backend sends review text to Azure Cognitive Services (or OpenAI).
   - Sentiment results (Positive/Neutral/Negative) stored in DB.

4. **AI Summary Generation**
   - Backend sends all reviews for the period to AI service.
   - Generates a structured summary with:
     - Positive trends
     - Negative trends
     - Recommended actions
   - Stores summary in DB.

5. **Email Delivery**
   - On schedule (weekly/monthly), backend generates HTML report.
   - Sends via Resend API to the user.

6. **Dashboard**
   - Frontend fetches reviews, sentiment data, and summaries from Supabase via backend API.
   - Displays in charts and tables.

---

### Architecture Diagram (Text Form)
