ReviewPulse MVP User Stories
User Story 1: Business Onboarding & Authentication
Title: Business Owner can securely sign up and log in.

User Role: As a business owner.

Goal: I want to securely sign up and log in with my email and password or a social account so that I can access my ReviewPulse dashboard.

State: Open

Deliverables:

Functional user registration via email/password.

Functional user login via email/password.

Integration with social login providers (e.g., Google, Facebook).

Secure JWT-based authentication for API calls.

Test Cases:

A new user can successfully sign up with a valid email and password.

An existing user can log in with their credentials.

A user can sign up and log in using a social media account.

An invalid password or email prevents a user from logging in.

The user's session remains secure after login.

User Story 2: Google Reviews Aggregation
Title: Business Owner can connect their Google Business Profile.

User Role: As a business owner.

Goal: I want to input my Google Place ID or business details so that ReviewPulse can fetch my business's Google Reviews.

State: Open

Deliverables:

An input form to accept a Google Place ID or business name/location.

Backend logic to call the Google Places API to fetch reviews.

Storage of raw review data (text, rating, date, author) in the Supabase database.

Test Cases:

The system successfully retrieves reviews for a valid Google Place ID.

The raw text, rating, date, and author of reviews are correctly stored.

The system handles cases where no reviews are found for a given business.

An error message is displayed for an invalid Place ID.

User Story 3: Sentiment Analysis
Title: Business Owner can view the sentiment of their reviews.

User Role: As a business owner.

Goal: I want each of my fetched reviews to be automatically classified by sentiment so that I can quickly understand the overall mood of my customers.

State: Open

Deliverables:

Integration with an AI service (Azure Cognitive Services or OpenAI) for sentiment analysis.

Classification of each review as Positive, Neutral, or Negative.

Storage of the sentiment result alongside each review in the database.

Test Cases:

A review with positive language is correctly tagged as "Positive."

A review with negative language is correctly tagged as "Negative."

A review with neutral or mixed language is correctly tagged as "Neutral."

The sentiment classification is consistently applied to all fetched reviews.

User Story 4: AI Summarization
Title: Business Owner can get a summary of their reviews.

User Role: As a business owner.

Goal: I want an AI-generated summary of my reviews, highlighting positive and negative trends, so that I know what to focus on to improve my business.

State: Open

Deliverables:

Backend logic to send a collection of reviews to an AI service for summarization.

The generated summary must include sections for "Positive themes," "Negative themes," and "Recommended improvements."

Storage of the generated summary in the Supabase database.

Test Cases:

The AI summary accurately reflects the key themes and trends present in the reviews for a given period.

The summary clearly separates positive and negative feedback.

The "Recommended improvements" section provides actionable advice based on the negative feedback.

User Story 5: Email Reporting
Title: Business Owner can receive automated reports.

User Role: As a business owner.

Goal: I want to receive a weekly or monthly email report with a summary of my reviews so that I can stay up-to-date on customer feedback without constantly checking the dashboard.

State: Open

Deliverables:

A scheduled job to generate reports (weekly/monthly).

An HTML email template that includes a sentiment distribution chart and the AI summary themes.

Integration with the Resend API to send the formatted email.

A link in the email that directs the user to the full dashboard.

Test Cases:

A test email is successfully sent to the business owner's email address.

The email is well-formatted and includes a sentiment chart and the top themes.

The link in the email correctly navigates to the ReviewPulse dashboard.

The reports are sent on the correct schedule (e.g., every Monday for weekly reports).

User Story 6: Simple Dashboard
Title: Business Owner can see a simple dashboard.

User Role: As a business owner.

Goal: I want a dashboard with a review feed, sentiment charts, and the AI summary so that I can easily view and filter my customer feedback.

State: Open

Deliverables:

A front-end dashboard built with Next.js and shadcn UI.

A review list view that displays each review with its corresponding sentiment badge.

A sentiment chart (line or bar) that visualizes sentiment over time.

A dedicated section to display the AI-generated summary.

A date range filter that allows users to view reviews and charts for a selected period.

Test Cases:

The dashboard loads and displays a list of reviews with their sentiment.

The sentiment chart correctly reflects the sentiment distribution of the reviews.

The AI summary box displays the most recent summary.

The date range filter updates the review list, chart, and summary when a new range is selected.