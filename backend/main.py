from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import os
import time
from dotenv import load_dotenv

from app.routers import auth, reviews, summaries, businesses
from app.config import settings

load_dotenv()

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    print("Starting ReviewPulse API...")
    yield
    # Shutdown
    print("Shutting down ReviewPulse API...")

app = FastAPI(
    title="ReviewPulse API",
    description="AI-powered Google Reviews aggregation and sentiment analysis",
    version="1.0.0",
    lifespan=lifespan
)

@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()
    print(f"\n🌐 ==> INCOMING REQUEST")
    print(f"📍 {request.method} {request.url}")
    print(f"🔍 Client: {request.client.host if request.client else 'Unknown'}")
    print(f"📋 Headers: {dict(request.headers)}")
    
    response = await call_next(request)
    
    process_time = time.time() - start_time
    print(f"⏱️ Processed in {process_time:.3f}s")
    print(f"📊 Response: {response.status_code}")
    
    return response

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:3001", "https://reviewpulse.vercel.app"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(auth.router, prefix="/api/auth", tags=["authentication"])
app.include_router(reviews.router, prefix="/api/reviews", tags=["reviews"])
app.include_router(summaries.router, prefix="/api/summaries", tags=["summaries"])
app.include_router(businesses.router, prefix="/api/businesses", tags=["businesses"])

@app.get("/")
async def root():
    return {"message": "ReviewPulse API is running"}

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

@app.get("/test-logging")
async def test_logging():
    """Test endpoint to demonstrate console logging functionality"""
    from app.services.email_service import EmailService
    
    # Sample data for testing
    sample_summary_data = {
        "positive_themes": [
            "Excellent customer service and friendly staff",
            "Great food quality and fresh ingredients", 
            "Clean and welcoming atmosphere"
        ],
        "negative_themes": [
            "Long wait times during peak hours",
            "Limited parking availability",
            "Some dishes are overpriced"
        ],
        "recommended_improvements": [
            "Consider hiring additional staff during busy periods",
            "Explore partnership with nearby parking facilities",
            "Review pricing strategy for premium dishes"
        ]
    }
    
    sample_stats = {
        "total": 45,
        "positive": 28,
        "negative": 12,
        "neutral": 5
    }
    
    # Test the console logging
    email_service = EmailService()
    success = await email_service.send_review_summary_report(
        to_email="<EMAIL>",
        business_name="Demo Restaurant",
        summary_data=sample_summary_data,
        stats=sample_stats,
        user_id="test-user-123"
    )
    
    return {
        "message": "Console logging test completed", 
        "success": success,
        "note": "Check the server console for the formatted analysis output"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)