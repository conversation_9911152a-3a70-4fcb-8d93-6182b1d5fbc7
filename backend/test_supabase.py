#!/usr/bin/env python3
"""
Test Supabase connection and database setup
"""

import os
from supabase import create_client
from dotenv import load_dotenv

load_dotenv()

def test_connection():
    """Test Supabase connection"""
    try:
        # Test with anon key first
        url = os.getenv("SUPABASE_URL")
        anon_key = os.getenv("SUPABASE_ANON_KEY")
        service_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
        
        print(f"🔗 Connecting to: {url}")
        
        # Test anon connection
        print("🔑 Testing anon key connection...")
        supabase_anon = create_client(url, anon_key)
        print("✅ Anon key connection successful")
        
        # Test service role connection
        print("🔑 Testing service role key connection...")
        supabase_service = create_client(url, service_key)
        print("✅ Service role key connection successful")
        
        # Test table existence
        print("📋 Checking existing tables...")
        try:
            # Try to query each table to see if it exists
            tables_to_check = ["reviews", "summaries", "business_profiles", "email_reports"]
            
            for table in tables_to_check:
                try:
                    result = supabase_service.table(table).select("*").limit(1).execute()
                    print(f"   ✅ Table '{table}' exists")
                except Exception as e:
                    print(f"   ❌ Table '{table}' missing or inaccessible: {str(e)}")
                    
        except Exception as e:
            print(f"   ⚠️  Could not check tables: {str(e)}")
            
        return True
        
    except Exception as e:
        print(f"❌ Connection failed: {str(e)}")
        return False

def create_missing_tables():
    """Create missing tables using SQL statements"""
    url = os.getenv("SUPABASE_URL")
    service_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
    
    supabase = create_client(url, service_key)
    
    # Since Supabase Python client doesn't support raw SQL execution,
    # we'll provide instructions for manual setup
    
    print("\n📝 Database setup instructions:")
    print("=" * 50)
    print("1. Go to your Supabase Dashboard")
    print("2. Navigate to SQL Editor")  
    print("3. Copy and paste the following SQL:")
    print()
    
    schema_sql = """
-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create custom types
DO $$ BEGIN
    CREATE TYPE sentiment_type AS ENUM ('positive', 'negative', 'neutral');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Email reports table (for logging analysis instead of sending emails)
CREATE TABLE IF NOT EXISTS email_reports (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    business_name VARCHAR NOT NULL,
    recipient_email VARCHAR NOT NULL,
    analysis_data JSONB NOT NULL,
    report_type VARCHAR DEFAULT 'email_summary',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_email_reports_user_id ON email_reports(user_id);
CREATE INDEX IF NOT EXISTS idx_email_reports_created_at ON email_reports(created_at);

-- Row Level Security (RLS) policies
ALTER TABLE email_reports ENABLE ROW LEVEL SECURITY;

-- Policies for email_reports
DROP POLICY IF EXISTS "Users can view their own email reports" ON email_reports;
CREATE POLICY "Users can view their own email reports" ON email_reports
    FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert their own email reports" ON email_reports;
CREATE POLICY "Users can insert their own email reports" ON email_reports
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Trigger for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

DROP TRIGGER IF EXISTS update_email_reports_updated_at ON email_reports;
CREATE TRIGGER update_email_reports_updated_at BEFORE UPDATE ON email_reports 
    FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();
"""
    
    print(schema_sql)
    print("=" * 50)
    print("4. Click 'Run' to execute the SQL")
    print("5. Return here and run the script again to verify setup")

if __name__ == "__main__":
    print("🧪 Testing ReviewPulse database setup...")
    
    if test_connection():
        print("\n🎉 Database connection successful!")
        print("If tables are missing, follow the setup instructions below.")
        create_missing_tables()
    else:
        print("\n💡 Check your .env file and ensure SUPABASE_URL and keys are correct")