#!/usr/bin/env python3
"""
Complete workflow test for ReviewPulse API
This script tests the entire user journey and core functionality
"""

import requests
import json
import time
from datetime import datetime, timedelta

# Configuration
BASE_URL = "http://localhost:8000"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "testpassword123"

def make_request(method, endpoint, headers=None, data=None, params=None):
    """Helper function to make API requests"""
    url = f"{BASE_URL}{endpoint}"
    try:
        response = requests.request(method, url, headers=headers, json=data, params=params)
        return response.status_code, response.json() if response.text else None
    except Exception as e:
        return None, str(e)

def test_authentication():
    """Test user registration and authentication"""
    print("🔐 Testing Authentication...")
    
    # Register new user
    print("   → Registering new user...")
    status, data = make_request("POST", "/api/auth/register", 
                               data={"email": TEST_EMAIL, "password": TEST_PASSWORD})
    
    if status == 200 and "access_token" in data:
        token = data["access_token"]
        print(f"   ✅ Registration successful")
        
        # Test getting current user
        print("   → Testing current user endpoint...")
        headers = {"Authorization": f"Bearer {token}"}
        status, user_data = make_request("GET", "/api/auth/me", headers=headers)
        
        if status == 200:
            print(f"   ✅ Current user: {user_data['user']['email']}")
            return token
        else:
            print(f"   ❌ Failed to get current user: {status}")
            return None
    else:
        print(f"   ❌ Registration failed: {status} - {data}")
        return None

def test_reviews_api(token):
    """Test reviews API endpoints"""
    print("\n📋 Testing Reviews API...")
    headers = {"Authorization": f"Bearer {token}"}
    
    # Get reviews (should be empty)
    print("   → Getting reviews...")
    status, data = make_request("GET", "/api/reviews/", headers=headers)
    
    if status == 200:
        print(f"   ✅ Reviews retrieved: {len(data)} reviews found")
    else:
        print(f"   ❌ Failed to get reviews: {status}")
        
    # Get stats
    print("   → Getting review statistics...")
    status, data = make_request("GET", "/api/reviews/stats", headers=headers)
    
    if status == 200:
        print(f"   ✅ Stats retrieved: {data}")
    else:
        print(f"   ❌ Failed to get stats: {status}")

def test_summaries_api(token):
    """Test summaries API endpoints"""
    print("\n📊 Testing Summaries API...")
    headers = {"Authorization": f"Bearer {token}"}
    
    # Get summaries (should be empty)
    print("   → Getting summaries...")
    status, data = make_request("GET", "/api/summaries/", headers=headers)
    
    if status == 200:
        print(f"   ✅ Summaries retrieved: {len(data)} summaries found")
    else:
        print(f"   ❌ Failed to get summaries: {status}")

def test_console_logging(token):
    """Test console logging functionality"""
    print("\n📺 Testing Console Logging...")
    headers = {"Authorization": f"Bearer {token}"}
    
    # Test our custom test endpoint
    print("   → Testing custom logging endpoint...")
    status, data = make_request("GET", "/test-logging", headers=headers)
    
    if status == 200:
        print(f"   ✅ Console logging test completed")
        print(f"   📝 Check server console for formatted analysis output")
        return True
    else:
        print(f"   ❌ Console logging test failed: {status}")
        return False

def test_api_documentation():
    """Test API documentation endpoint"""
    print("\n📚 Testing API Documentation...")
    
    try:
        response = requests.get(f"{BASE_URL}/docs")
        if response.status_code == 200:
            print("   ✅ Swagger UI accessible at /docs")
        else:
            print(f"   ❌ Swagger UI not accessible: {response.status_code}")
            
        response = requests.get(f"{BASE_URL}/openapi.json")
        if response.status_code == 200:
            print("   ✅ OpenAPI spec accessible at /openapi.json")
        else:
            print(f"   ❌ OpenAPI spec not accessible: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Documentation test failed: {e}")

def test_error_handling():
    """Test error handling"""
    print("\n🛡️ Testing Error Handling...")
    
    # Test invalid token
    print("   → Testing invalid authentication...")
    headers = {"Authorization": "Bearer invalid-token"}
    status, data = make_request("GET", "/api/reviews/", headers=headers)
    
    if status == 401:
        print("   ✅ Invalid token properly rejected")
    else:
        print(f"   ❌ Invalid token handling failed: {status}")
        
    # Test missing authentication
    print("   → Testing missing authentication...")
    status, data = make_request("GET", "/api/reviews/")
    
    if status == 403:
        print("   ✅ Missing authentication properly rejected")
    else:
        print(f"   ❌ Missing authentication handling failed: {status}")

def main():
    """Run complete workflow test"""
    print("🚀 ReviewPulse API Workflow Test")
    print("=" * 50)
    
    start_time = datetime.now()
    
    # Test authentication
    token = test_authentication()
    if not token:
        print("\n❌ Authentication failed - stopping tests")
        return 1
    
    # Test all API endpoints
    test_reviews_api(token)
    test_summaries_api(token)
    test_console_logging(token)
    test_api_documentation()
    test_error_handling()
    
    # Summary
    end_time = datetime.now()
    duration = end_time - start_time
    
    print("\n" + "=" * 50)
    print("🎉 Workflow Test Complete!")
    print(f"⏱️  Duration: {duration.total_seconds():.2f} seconds")
    print("\n📋 What was tested:")
    print("   ✅ User registration and authentication")
    print("   ✅ JWT token generation and validation")
    print("   ✅ Protected API endpoints")
    print("   ✅ Reviews API (empty data)")
    print("   ✅ Summaries API (empty data)")
    print("   ✅ Console logging functionality")
    print("   ✅ API documentation accessibility")
    print("   ✅ Error handling and security")
    
    print("\n🔍 Next steps for full testing:")
    print("   1. Add Google Places API integration")
    print("   2. Test with real review data")
    print("   3. Test AI sentiment analysis")
    print("   4. Test AI summarization")
    print("   5. Test full console logging with real data")
    
    return 0

if __name__ == "__main__":
    exit(main())