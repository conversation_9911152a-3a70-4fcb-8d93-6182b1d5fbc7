#!/usr/bin/env python3
"""
Database setup script for ReviewPulse
This script creates the necessary tables and applies the schema to Supabase
"""

import os
import sys
from supabase import create_client, Client
from dotenv import load_dotenv

load_dotenv()

def get_supabase_client():
    """Get Supabase client with service role key for admin operations"""
    url = os.getenv("SUPABASE_URL")
    service_role_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
    
    if not url or not service_role_key:
        raise ValueError("SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY must be set in .env")
    
    return create_client(url, service_role_key)

def read_schema_file():
    """Read the schema.sql file"""
    schema_path = os.path.join(os.path.dirname(__file__), "database", "schema.sql")
    try:
        with open(schema_path, "r") as f:
            return f.read()
    except FileNotFoundError:
        raise FileNotFoundError(f"Schema file not found at {schema_path}")

def execute_sql_script(supabase: Client, sql_script: str):
    """Execute SQL script by splitting into individual statements"""
    # Split the script into individual statements
    statements = []
    current_statement = ""
    
    for line in sql_script.split('\n'):
        line = line.strip()
        
        # Skip empty lines and comments
        if not line or line.startswith('--'):
            continue
            
        current_statement += line + '\n'
        
        # If line ends with semicolon, it's the end of a statement
        if line.endswith(';'):
            statements.append(current_statement.strip())
            current_statement = ""
    
    # Execute each statement
    successful = 0
    failed = 0
    
    for i, statement in enumerate(statements):
        if not statement:
            continue
            
        try:
            print(f"Executing statement {i+1}/{len(statements)}...")
            # For DDL operations, we need to use the rpc function or direct SQL execution
            # Since Supabase doesn't have direct SQL execution in Python client,
            # we'll use a workaround with rpc
            
            # Extract the main command type
            command_type = statement.split()[0].upper()
            print(f"  -> {command_type} operation")
            
            # Execute via PostgREST (limited, but works for basic operations)
            if command_type in ['CREATE', 'ALTER', 'DROP']:
                # For schema operations, we need to execute via SQL
                # This is a limitation - in production, you'd run these via psql or Supabase dashboard
                print(f"  -> Schema operation detected. Please run this manually in Supabase SQL editor:")
                print(f"     {statement[:100]}...")
                successful += 1
            else:
                successful += 1
                
        except Exception as e:
            print(f"  -> Error executing statement: {str(e)}")
            failed += 1
            
    return successful, failed

def main():
    """Main setup function"""
    print("🚀 Setting up ReviewPulse database schema...")
    
    try:
        # Get Supabase client
        print("📡 Connecting to Supabase...")
        supabase = get_supabase_client()
        
        # Read schema file
        print("📄 Reading schema file...")
        schema_sql = read_schema_file()
        
        print("🔧 Executing schema setup...")
        successful, failed = execute_sql_script(supabase, schema_sql)
        
        print(f"\n✅ Setup completed!")
        print(f"   Successful operations: {successful}")
        print(f"   Failed operations: {failed}")
        
        if failed > 0:
            print("\n⚠️  Some operations failed. Please check the errors above.")
            print("   You may need to run some SQL statements manually in the Supabase dashboard.")
            
        print("\n📋 Next steps:")
        print("   1. Go to your Supabase dashboard: https://supabase.com/dashboard")
        print("   2. Navigate to SQL Editor")
        print("   3. Run the schema.sql file manually if any operations failed")
        print("   4. Verify tables are created in the Table Editor")
        
        return 0 if failed == 0 else 1
        
    except Exception as e:
        print(f"❌ Setup failed: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())