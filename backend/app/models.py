from pydantic import BaseModel
from datetime import datetime
from typing import Optional, List
from enum import Enum

class SentimentType(str, Enum):
    POSITIVE = "positive"
    NEGATIVE = "negative"
    NEUTRAL = "neutral"

class UserBase(BaseModel):
    email: str

class UserCreate(UserBase):
    password: str

class User(UserBase):
    id: str
    created_at: datetime
    
    class Config:
        from_attributes = True

class BusinessProfile(BaseModel):
    id: Optional[str] = None
    user_id: str
    google_place_id: str
    business_name: str
    business_address: Optional[str] = None
    created_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class ReviewBase(BaseModel):
    google_place_id: str
    review_text: str
    rating: int
    author_name: Optional[str] = None
    review_date: datetime
    google_review_id: str

class ReviewCreate(ReviewBase):
    pass

class Review(ReviewBase):
    id: str
    user_id: str
    sentiment: Optional[SentimentType] = None
    created_at: datetime
    
    class Config:
        from_attributes = True

class SentimentAnalysis(BaseModel):
    review_id: str
    sentiment: SentimentType
    confidence_score: float
    created_at: Optional[datetime] = None

class SummaryBase(BaseModel):
    user_id: str
    period_start: datetime
    period_end: datetime
    positive_themes: List[str]
    negative_themes: List[str]
    recommended_improvements: List[str]
    total_reviews: int
    sentiment_distribution: dict

class SummaryCreate(SummaryBase):
    pass

class Summary(SummaryBase):
    id: str
    created_at: datetime
    
    class Config:
        from_attributes = True

class Token(BaseModel):
    access_token: str
    token_type: str

class TokenData(BaseModel):
    email: Optional[str] = None

class BusinessInfo(BaseModel):
    id: Optional[str] = None
    user_id: str
    google_place_id: str
    business_name: str
    business_address: Optional[str] = None
    phone_number: Optional[str] = None
    website: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class GoogleMapsUrlRequest(BaseModel):
    google_maps_url: str

class BusinessSearchResponse(BaseModel):
    place_id: str
    name: str
    address: Optional[str] = None
    phone: Optional[str] = None
    website: Optional[str] = None
    rating: Optional[float] = None
    total_reviews: Optional[int] = None
    types: Optional[List[str]] = None

class GooglePlaceRequest(BaseModel):
    google_place_id: str
    business_name: Optional[str] = None