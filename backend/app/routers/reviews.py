from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Optional
from datetime import datetime
import uuid

from app.models import Review, GooglePlaceRequest
from app.routers.auth import get_current_user
from app.services.google_places import GooglePlacesService
from app.services.sentiment_analysis import SentimentAnalysisService
from app.database import get_supabase, get_supabase_admin

router = APIRouter()

@router.get("/test")
async def test_endpoint():
    """Simple test endpoint"""
    return {"message": "Reviews API is working!", "status": "success"}


@router.post("/fetch")
async def fetch_reviews(
    place_request: GooglePlaceRequest,
    current_user = Depends(get_current_user)
):
    """Fetch reviews from Google Places API and store them"""
    print(f"DEBUG: Received request for place_id: {place_request.google_place_id}")
    print(f"DEBUG: User: {current_user['email']}")
    try:
        supabase = get_supabase_admin()
        
        # First, check if we have this business in our database
        business_result = supabase.table("businesses").select("*").eq(
            "google_place_id", place_request.google_place_id
        ).execute()
        
        if business_result.data:
            business_name = business_result.data[0]['business_name']
            print(f"DEBUG: Found business in database: {business_name}")
        else:
            print(f"DEBUG: Business not found in database, using Place ID: {place_request.google_place_id}")
            business_name = place_request.business_name or "Unknown Business"
        
        # Fetch real reviews from Google Places API
        google_service = GooglePlacesService()
        reviews = await google_service.fetch_reviews(place_request.google_place_id)
        print(f"DEBUG: Fetched {len(reviews)} reviews from Google Places API")
        
        print("DEBUG: Got Supabase admin client")
        stored_reviews = []
        
        for review_data in reviews:
            # Check if review already exists
            existing = supabase.table("reviews").select("*").eq(
                "google_review_id", review_data["google_review_id"]
            ).eq("user_id", current_user["id"]).execute()
            
            if not existing.data:
                # Store new review
                review_record = {
                    "user_id": current_user["id"],
                    "google_place_id": place_request.google_place_id,
                    "review_text": review_data["review_text"],
                    "rating": review_data["rating"],
                    "author_name": review_data.get("author_name"),
                    "review_date": review_data["review_date"],
                    "google_review_id": review_data["google_review_id"],
                    "created_at": datetime.utcnow().isoformat()
                }
                
                result = supabase.table("reviews").insert(review_record).execute()
                if result.data:
                    stored_reviews.extend(result.data)
                    print(f"DEBUG: Stored review: {review_data['author_name']} - {review_data['rating']} stars")
        
        # Analyze sentiment for new reviews
        sentiment_service = SentimentAnalysisService()
        for review in stored_reviews:
            print(f"DEBUG: Analyzing sentiment for review: {review['id']}")
            sentiment_result = await sentiment_service.analyze_sentiment(review["review_text"])
            supabase.table("reviews").update({
                "sentiment": sentiment_result["sentiment"]
            }).eq("id", review["id"]).execute()
            print(f"DEBUG: Sentiment: {sentiment_result['sentiment']}")
        
        return {
            "message": f"Fetched and stored {len(stored_reviews)} new reviews",
            "total_reviews": len(stored_reviews)
        }
        
    except Exception as e:
        print(f"DEBUG: Error occurred: {str(e)}")
        print(f"DEBUG: Error type: {type(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error fetching reviews: {str(e)}")

@router.get("/", response_model=List[Review])
async def get_reviews(
    current_user = Depends(get_current_user),
    google_place_id: Optional[str] = Query(None),
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
    sentiment: Optional[str] = Query(None)
):
    """Get reviews with optional filters"""
    supabase = get_supabase_admin()
    
    query = supabase.table("reviews").select("*").eq("user_id", current_user["id"])
    
    if google_place_id:
        query = query.eq("google_place_id", google_place_id)
    if start_date:
        query = query.gte("review_date", start_date.isoformat())
    if end_date:
        query = query.lte("review_date", end_date.isoformat())
    if sentiment:
        query = query.eq("sentiment", sentiment)
    
    result = query.order("review_date", desc=True).execute()
    return result.data

@router.get("/stats")
async def get_review_stats(
    current_user = Depends(get_current_user),
    google_place_id: Optional[str] = Query(None),
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None)
):
    """Get review statistics"""
    supabase = get_supabase_admin()
    
    query = supabase.table("reviews").select("sentiment").eq("user_id", current_user["id"])
    
    if google_place_id:
        query = query.eq("google_place_id", google_place_id)
    if start_date:
        query = query.gte("review_date", start_date.isoformat())
    if end_date:
        query = query.lte("review_date", end_date.isoformat())
    
    result = query.execute()
    
    stats = {
        "total": len(result.data),
        "positive": len([r for r in result.data if r["sentiment"] == "positive"]),
        "negative": len([r for r in result.data if r["sentiment"] == "negative"]),
        "neutral": len([r for r in result.data if r["sentiment"] == "neutral"])
    }
    
    return stats

@router.post("/fetch-by-business")
async def fetch_reviews_by_business(
    request: dict,  # {"business_id": "uuid"}
    current_user = Depends(get_current_user)
):
    """Fetch reviews for a business from user's saved businesses"""
    business_id = request.get("business_id")
    if not business_id:
        raise HTTPException(status_code=400, detail="business_id is required")
    
    try:
        supabase = get_supabase_admin()
        
        # Verify user has access to this business
        business_profile = supabase.table("business_profiles").select(
            "*, businesses(*)"
        ).eq("user_id", current_user["id"]).eq("business_id", business_id).execute()
        
        if not business_profile.data:
            raise HTTPException(status_code=404, detail="Business not found in your saved businesses")
        
        business = business_profile.data[0]['businesses']
        google_place_id = business['google_place_id']
        
        print(f"DEBUG: Fetching reviews for business: {business['business_name']}")
        
        # Use the existing fetch logic
        place_request = GooglePlaceRequest(
            google_place_id=google_place_id,
            business_name=business['business_name']
        )
        
        # Call the existing fetch_reviews function logic
        google_service = GooglePlacesService()
        reviews = await google_service.fetch_reviews(google_place_id)
        
        stored_reviews = []
        for review_data in reviews:
            # Check if review already exists
            existing = supabase.table("reviews").select("*").eq(
                "google_review_id", review_data["google_review_id"]
            ).eq("user_id", current_user["id"]).execute()
            
            if not existing.data:
                # Store new review
                review_record = {
                    "user_id": current_user["id"],
                    "google_place_id": google_place_id,
                    "review_text": review_data["review_text"],
                    "rating": review_data["rating"],
                    "author_name": review_data.get("author_name"),
                    "review_date": review_data["review_date"],
                    "google_review_id": review_data["google_review_id"],
                    "created_at": datetime.utcnow().isoformat()
                }
                
                result = supabase.table("reviews").insert(review_record).execute()
                if result.data:
                    stored_reviews.extend(result.data)
        
        # Analyze sentiment for new reviews
        sentiment_service = SentimentAnalysisService()
        for review in stored_reviews:
            sentiment_result = await sentiment_service.analyze_sentiment(review["review_text"])
            supabase.table("reviews").update({
                "sentiment": sentiment_result["sentiment"]
            }).eq("id", review["id"]).execute()
        
        return {
            "message": f"Fetched and stored {len(stored_reviews)} new reviews for {business['business_name']}",
            "total_reviews": len(stored_reviews),
            "business_name": business['business_name']
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"DEBUG: Error fetching reviews by business: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error fetching reviews: {str(e)}")

@router.get("/place/{place_id}")
async def get_reviews_for_place(
    place_id: str,
    current_user = Depends(get_current_user)
):
    """Get reviews and stats for a specific place ID in the format expected by frontend"""
    supabase = get_supabase_admin()
    print(f"DEBUG: Getting reviews for place_id: {place_id}, user: {current_user['email']}")
    
    # Get reviews for the place for this user
    reviews_result = supabase.table("reviews").select("*").eq(
        "user_id", current_user["id"]
    ).eq("google_place_id", place_id).order("review_date", desc=True).execute()
    
    reviews = reviews_result.data
    print(f"DEBUG: Found {len(reviews)} reviews")
    
    # Calculate stats
    total = len(reviews)
    if total == 0:
        print("DEBUG: No reviews found")
        return {
            "reviews": [],
            "total": 0,
            "average_rating": 0.0
        }
    
    # Calculate average rating
    total_rating = sum(review["rating"] for review in reviews)
    average_rating = total_rating / total
    
    # Transform reviews to match frontend interface
    transformed_reviews = []
    for review in reviews:
        transformed_reviews.append({
            "id": review["id"],
            "place_id": review["google_place_id"],
            "author_name": review["author_name"] or "Anonymous",
            "rating": review["rating"],
            "text": review["review_text"],
            "time": review["review_date"],
            "sentiment": review.get("sentiment"),
            "sentiment_score": review.get("sentiment_score")
        })
    
    return {
        "reviews": transformed_reviews,
        "total": total,
        "average_rating": round(average_rating, 1)
    }