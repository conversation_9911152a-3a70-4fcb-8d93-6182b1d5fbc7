from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Optional
from datetime import datetime, timedelta

from app.models import Summary
from app.routers.auth import get_current_user
from app.services.ai_summarization import AISummarizationService
from app.services.email_service import EmailService
from app.database import get_supabase

router = APIRouter()

@router.get("/test")
async def test_summaries_endpoint():
    """Simple test endpoint for summaries"""
    return {"message": "Summaries API is working!", "status": "success"}

@router.post("/generate")
async def generate_summary(
    current_user = Depends(get_current_user),
    google_place_id: Optional[str] = Query(None),
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None)
):
    """Generate AI summary for reviews in a given period"""
    supabase = get_supabase()
    
    # Default to last 30 days if no dates provided
    if not end_date:
        end_date = datetime.utcnow()
    if not start_date:
        start_date = end_date - timedelta(days=30)
    
    # Fetch reviews for the period
    query = supabase.table("reviews").select("*").eq("user_id", current_user["id"])
    
    if google_place_id:
        query = query.eq("google_place_id", google_place_id)
    
    query = query.gte("review_date", start_date.isoformat()).lte("review_date", end_date.isoformat())
    
    reviews_result = query.execute()
    reviews = reviews_result.data
    
    if not reviews:
        raise HTTPException(status_code=404, detail="No reviews found for the specified period")
    
    try:
        # Generate AI summary
        ai_service = AISummarizationService()
        summary_data = await ai_service.generate_summary(reviews)
        
        # Calculate sentiment distribution
        sentiment_stats = {
            "positive": len([r for r in reviews if r["sentiment"] == "positive"]),
            "negative": len([r for r in reviews if r["sentiment"] == "negative"]),
            "neutral": len([r for r in reviews if r["sentiment"] == "neutral"])
        }
        
        # Store summary in database
        summary_record = {
            "user_id": current_user["id"],
            "period_start": start_date.isoformat(),
            "period_end": end_date.isoformat(),
            "positive_themes": summary_data["positive_themes"],
            "negative_themes": summary_data["negative_themes"],
            "recommended_improvements": summary_data["recommended_improvements"],
            "total_reviews": len(reviews),
            "sentiment_distribution": sentiment_stats,
            "created_at": datetime.utcnow().isoformat()
        }
        
        result = supabase.table("summaries").insert(summary_record).execute()
        
        return result.data[0] if result.data else summary_record
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error generating summary: {str(e)}")

@router.get("/")
async def get_summaries(
    current_user = Depends(get_current_user),
    google_place_id: Optional[str] = Query(None),
    limit: int = Query(10, le=100)
):
    """Get user's summaries"""
    supabase = get_supabase()
    print(f"DEBUG: Getting summaries for user: {current_user['email']}")
    
    query = supabase.table("summaries").select("*").eq("user_id", current_user["id"])
    
    if google_place_id:
        print(f"DEBUG: Filtering summaries by place_id: {google_place_id}")
        # Add place_id filter when summaries include place tracking
        # For now return empty array as we don't store place_id in summaries yet
        return []
    
    result = query.order("created_at", desc=True).limit(limit).execute()
    print(f"DEBUG: Found {len(result.data)} summaries")
    
    return result.data

@router.get("/latest")
async def get_latest_summary(current_user = Depends(get_current_user)):
    """Get the most recent summary"""
    supabase = get_supabase()
    
    result = supabase.table("summaries").select("*").eq(
        "user_id", current_user["id"]
    ).order("created_at", desc=True).limit(1).execute()
    
    if not result.data:
        raise HTTPException(status_code=404, detail="No summaries found")
    
    return result.data[0]

@router.post("/send-report")
async def send_email_report(
    current_user = Depends(get_current_user),
    business_name: str = Query(..., description="Business name for the report"),
    google_place_id: Optional[str] = Query(None),
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None)
):
    """Send email report with latest summary"""
    supabase = get_supabase()
    
    # Default to last 30 days if no dates provided
    if not end_date:
        end_date = datetime.utcnow()
    if not start_date:
        start_date = end_date - timedelta(days=30)
    
    # Get reviews for stats
    query = supabase.table("reviews").select("sentiment").eq("user_id", current_user["id"])
    
    if google_place_id:
        query = query.eq("google_place_id", google_place_id)
    
    query = query.gte("review_date", start_date.isoformat()).lte("review_date", end_date.isoformat())
    
    reviews_result = query.execute()
    reviews = reviews_result.data
    
    if not reviews:
        raise HTTPException(status_code=404, detail="No reviews found for the specified period")
    
    # Calculate stats
    stats = {
        "total": len(reviews),
        "positive": len([r for r in reviews if r["sentiment"] == "positive"]),
        "negative": len([r for r in reviews if r["sentiment"] == "negative"]),
        "neutral": len([r for r in reviews if r["sentiment"] == "neutral"])
    }
    
    # Get latest summary
    summary_result = supabase.table("summaries").select("*").eq(
        "user_id", current_user["id"]
    ).order("created_at", desc=True).limit(1).execute()
    
    if not summary_result.data:
        raise HTTPException(status_code=404, detail="No summary found. Please generate a summary first.")
    
    summary_data = summary_result.data[0]
    
    try:
        # Send email
        email_service = EmailService()
        success = await email_service.send_review_summary_report(
            to_email=current_user["email"],
            business_name=business_name,
            summary_data=summary_data,
            stats=stats,
            user_id=current_user["id"]
        )
        
        if success:
            return {"message": "Report sent successfully", "email": current_user["email"]}
        else:
            raise HTTPException(status_code=500, detail="Failed to send report")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error sending report: {str(e)}")