from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTT<PERSON>Exception, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import JW<PERSON>rror, jwt
from datetime import datetime, timed<PERSON>ta
from typing import Optional
import hashlib

from app.models import User, <PERSON>r<PERSON><PERSON>, To<PERSON>, TokenData
from app.config import settings
from app.database import get_supabase

router = APIRouter()
security = HTTPBearer()

def verify_supabase_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Verify Supabase JWT token"""
    try:
        supabase = get_supabase()
        # Use Supabase to verify the token
        user_response = supabase.auth.get_user(credentials.credentials)
        
        if user_response.user:
            return user_response.user
        else:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token"
            )
    except Exception as e:
        print(f"Token verification error: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials"
        )

async def get_current_user(user = Depends(verify_supabase_token)):
    """Get current user from verified Supabase token"""
    return {
        "id": user.id,
        "email": user.email,
        "created_at": user.created_at
    }

@router.post("/register", response_model=Token)
async def register(user: UserCreate):
    supabase = get_supabase()
    try:
        response = supabase.auth.sign_up({
            "email": user.email,
            "password": user.password
        })
        
        if response.user and response.session:
            return {
                "access_token": response.session.access_token,
                "token_type": "bearer"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Registration failed"
            )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Registration failed: {str(e)}"
        )

@router.post("/login", response_model=Token)
async def login(user: UserCreate):
    supabase = get_supabase()
    try:
        response = supabase.auth.sign_in_with_password({
            "email": user.email,
            "password": user.password
        })
        
        if response.user and response.session:
            return {
                "access_token": response.session.access_token,
                "token_type": "bearer"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid credentials"
            )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid credentials"
        )

@router.get("/me", response_model=dict)
async def read_users_me(current_user = Depends(get_current_user)):
    return {"user": current_user}