from fastapi import APIRouter, Depends, HTTPException
from typing import List
from datetime import datetime
import uuid

from app.models import (
    GoogleMapsUrlRequest, 
    BusinessSearchResponse, 
    BusinessInfo
)
from app.routers.auth import get_current_user
from app.services.google_maps_parser import GoogleMapsParser
from app.services.business_info_fetcher import BusinessInfoFetcher
from app.database import get_supabase_admin

router = APIRouter()

@router.post("/search-from-url", response_model=BusinessSearchResponse)
async def search_business_from_url(
    request: GoogleMapsUrlRequest,
    current_user = Depends(get_current_user)
):
    """
    Extract business information from Google Maps URL
    """
    print(f"\n🚀 ==> BUSINESS SEARCH REQUEST")
    print(f"📧 User: {current_user.get('email', 'Unknown')}")
    print(f"🔗 URL: {request.google_maps_url}")
    print(f"📏 URL Length: {len(request.google_maps_url)} characters")
    
    try:
        # Parse the Google Maps URL
        print(f"🔍 Step 1: Parsing Google Maps URL...")
        parser = GoogleMapsParser()
        url_info = parser.extract_place_info_from_url(request.google_maps_url)
        
        if not url_info:
            print(f"❌ Failed to extract place info from URL")
            raise HTTPException(
                status_code=400, 
                detail="Could not extract location information from the provided Google Maps URL"
            )
        
        print(f"✅ Extracted URL info: {url_info}")
        
        # Resolve to Place ID
        print(f"🔍 Step 2: Resolving to Google Place ID...")
        place_id = await parser.resolve_to_place_id(url_info)
        
        if not place_id:
            print(f"❌ Failed to resolve to Place ID")
            raise HTTPException(
                status_code=400,
                detail="Could not resolve the URL to a valid Google Place ID"
            )
        
        print(f"✅ Resolved Place ID: {place_id}")
        
        # Fetch detailed business information
        print(f"🔍 Step 3: Fetching business details from Google Places API...")
        fetcher = BusinessInfoFetcher()
        business_info = await fetcher.get_place_details(place_id)
        
        if not business_info:
            print(f"❌ Failed to fetch business details")
            raise HTTPException(
                status_code=404,
                detail="Could not fetch business details from Google Places API"
            )
        
        print(f"✅ Fetched business details: {business_info.name}")
        
        # Validate that it's a legitimate business
        print(f"🔍 Step 4: Validating business...")
        if not fetcher.validate_business_info(business_info):
            print(f"❌ Business validation failed - not a reviewable business")
            print(f"   Types: {business_info.types}")
            print(f"   Reviews: {business_info.total_reviews}")
            raise HTTPException(
                status_code=400,
                detail="The provided URL does not appear to point to a reviewable business"
            )
        
        print(f"✅ Business validation passed!")
        print(f"🎉 SUCCESS: Found business '{business_info.name}' with {business_info.total_reviews} reviews")
        
        return business_info
        
    except HTTPException as he:
        print(f"❌ HTTP Exception: {he.detail}")
        raise
    except Exception as e:
        print(f"💥 UNEXPECTED ERROR: {str(e)}")
        print(f"🔍 Error type: {type(e).__name__}")
        import traceback
        print(f"📋 Traceback:")
        traceback.print_exc()
        raise HTTPException(
            status_code=500, 
            detail=f"Error processing Google Maps URL: {str(e)}"
        )

@router.post("/save")
async def save_business(
    business_data: BusinessSearchResponse,
    current_user = Depends(get_current_user)
):
    """
    Save business information to database after user confirmation
    """
    print(f"DEBUG: Saving business: {business_data.name} for user: {current_user['email']}")
    
    try:
        supabase = get_supabase_admin()
        
        # First, check if business already exists in businesses table
        existing_business = supabase.table("businesses").select("*").eq(
            "google_place_id", business_data.place_id
        ).execute()
        
        business_id = None
        
        if existing_business.data:
            # Business exists, use existing ID
            business_id = existing_business.data[0]['id']
            print(f"DEBUG: Business already exists with ID: {business_id}")
            
            # Update the business info with latest data
            update_data = {
                "business_name": business_data.name,
                "business_address": business_data.address,
                "phone_number": business_data.phone,
                "website": business_data.website,
                "updated_at": datetime.utcnow().isoformat()
            }
            
            supabase.table("businesses").update(update_data).eq(
                "id", business_id
            ).execute()
            
        else:
            # Create new business record
            business_record = {
                "google_place_id": business_data.place_id,
                "business_name": business_data.name,
                "business_address": business_data.address,
                "phone_number": business_data.phone,
                "website": business_data.website,
                "created_at": datetime.utcnow().isoformat()
            }
            
            result = supabase.table("businesses").insert(business_record).execute()
            
            if not result.data:
                raise HTTPException(status_code=500, detail="Failed to save business")
            
            business_id = result.data[0]['id']
            print(f"DEBUG: Created new business with ID: {business_id}")
        
        # Now check if user already has this business in their profile
        existing_profile = supabase.table("business_profiles").select("*").eq(
            "user_id", current_user["id"]
        ).eq("business_id", business_id).execute()
        
        if not existing_profile.data:
            # Create business profile for user
            profile_record = {
                "user_id": current_user["id"],
                "business_id": business_id,
                "created_at": datetime.utcnow().isoformat()
            }
            
            profile_result = supabase.table("business_profiles").insert(profile_record).execute()
            
            if not profile_result.data:
                raise HTTPException(status_code=500, detail="Failed to save business profile")
            
            print(f"DEBUG: Created business profile for user")
        else:
            print(f"DEBUG: User already has this business in their profile")
        
        return {
            "message": "Business saved successfully",
            "business_id": business_id,
            "place_id": business_data.place_id,
            "name": business_data.name
        }
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"DEBUG: Error saving business: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error saving business: {str(e)}"
        )

@router.get("/my-businesses", response_model=List[BusinessInfo])
async def get_my_businesses(current_user = Depends(get_current_user)):
    """
    Get all businesses saved by the current user
    """
    try:
        supabase = get_supabase_admin()
        
        # Join business_profiles with businesses table
        result = supabase.table("business_profiles").select(
            "*, businesses(*)"
        ).eq("user_id", current_user["id"]).execute()
        
        businesses = []
        for profile in result.data:
            business = profile['businesses']
            business_info = BusinessInfo(
                id=business['id'],
                user_id=current_user["id"],
                google_place_id=business['google_place_id'],
                business_name=business['business_name'],
                business_address=business.get('business_address'),
                phone_number=business.get('phone_number'),
                website=business.get('website'),
                created_at=business['created_at'],
                updated_at=business.get('updated_at')
            )
            businesses.append(business_info)
        
        return businesses
        
    except Exception as e:
        print(f"DEBUG: Error fetching user businesses: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error fetching businesses: {str(e)}"
        )

@router.delete("/my-businesses/{business_id}")
async def remove_business(
    business_id: str,
    current_user = Depends(get_current_user)
):
    """
    Remove a business from user's saved businesses
    """
    try:
        supabase = get_supabase_admin()
        
        # Remove from business_profiles (this doesn't delete the business itself)
        result = supabase.table("business_profiles").delete().eq(
            "user_id", current_user["id"]
        ).eq("business_id", business_id).execute()
        
        if not result.data:
            raise HTTPException(status_code=404, detail="Business not found in your saved businesses")
        
        return {"message": "Business removed from your saved businesses"}
        
    except HTTPException:
        raise
    except Exception as e:
        print(f"DEBUG: Error removing business: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error removing business: {str(e)}"
        )