import json
from typing import Dict, Any, List
from datetime import datetime
from app.config import settings
from app.database import get_supabase

class EmailService:
    def __init__(self):
        # No longer require Resend API key for console logging
        pass
    
    async def send_review_summary_report(
        self,
        to_email: str,
        business_name: str,
        summary_data: Dict[str, Any],
        stats: Dict[str, int],
        dashboard_url: str = "https://reviewpulse.vercel.app/dashboard",
        user_id: str = None
    ) -> bool:
        """Log review summary analysis to console and save to Supabase"""
        try:
            # Prepare the analysis data
            analysis_data = {
                "business_name": business_name,
                "recipient_email": to_email,
                "summary_data": summary_data,
                "stats": stats,
                "dashboard_url": dashboard_url,
                "generated_at": datetime.now().isoformat()
            }
            
            # Console log the analysis
            print("\n" + "="*60)
            print("📊 REVIEW SUMMARY ANALYSIS")
            print("="*60)
            print(f"Business: {business_name}")
            print(f"Report Date: {datetime.now().strftime('%B %d, %Y')}")
            print(f"Recipient: {to_email}")
            print(f"\n📈 REVIEW STATISTICS:")
            print(f"  Total Reviews: {stats.get('total', 0)}")
            print(f"  Positive: {stats.get('positive', 0)} ({round((stats.get('positive', 0) / max(stats.get('total', 1), 1)) * 100, 1)}%)")
            print(f"  Negative: {stats.get('negative', 0)} ({round((stats.get('negative', 0) / max(stats.get('total', 1), 1)) * 100, 1)}%)")
            print(f"  Neutral: {stats.get('neutral', 0)} ({round((stats.get('neutral', 0) / max(stats.get('total', 1), 1)) * 100, 1)}%)")
            
            print(f"\n✨ POSITIVE THEMES:")
            for theme in summary_data.get('positive_themes', []):
                print(f"  • {theme}")
            
            print(f"\n⚠️ NEGATIVE THEMES:")
            for theme in summary_data.get('negative_themes', []):
                print(f"  • {theme}")
            
            print(f"\n💡 RECOMMENDED IMPROVEMENTS:")
            for improvement in summary_data.get('recommended_improvements', []):
                print(f"  • {improvement}")
            
            print("="*60)
            print("Analysis logged successfully!")
            print("="*60 + "\n")
            
            # Save to Supabase
            supabase = get_supabase()
            report_record = {
                "user_id": user_id,
                "business_name": business_name,
                "recipient_email": to_email,
                "analysis_data": analysis_data,
                "report_type": "email_summary",
                "created_at": datetime.now().isoformat()
            }
            
            result = supabase.table("email_reports").insert(report_record).execute()
            
            if result.data:
                print(f"✅ Analysis saved to Supabase with ID: {result.data[0].get('id', 'Unknown')}")
            else:
                print("⚠️ Failed to save analysis to Supabase")
            
            return True
            
        except Exception as e:
            print(f"❌ Error processing analysis: {str(e)}")
            return False
    
    def _generate_report_html(
        self,
        business_name: str,
        summary_data: Dict[str, Any],
        stats: Dict[str, int],
        dashboard_url: str
    ) -> str:
        """Generate HTML content for the email report"""
        
        # Calculate percentages
        total = stats.get("total", 0)
        if total > 0:
            positive_pct = round((stats.get("positive", 0) / total) * 100, 1)
            negative_pct = round((stats.get("negative", 0) / total) * 100, 1)
            neutral_pct = round((stats.get("neutral", 0) / total) * 100, 1)
        else:
            positive_pct = negative_pct = neutral_pct = 0
        
        # Generate theme lists
        positive_themes_html = self._generate_theme_list(summary_data.get("positive_themes", []))
        negative_themes_html = self._generate_theme_list(summary_data.get("negative_themes", []))
        improvements_html = self._generate_theme_list(summary_data.get("recommended_improvements", []))
        
        html_content = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>ReviewPulse Report</title>
            <style>
                body {{
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                    background-color: #f8f9fa;
                }}
                .header {{
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 30px;
                    border-radius: 8px 8px 0 0;
                    text-align: center;
                }}
                .content {{
                    background: white;
                    padding: 30px;
                    border-radius: 0 0 8px 8px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }}
                .stats-grid {{
                    display: flex;
                    gap: 20px;
                    margin: 20px 0;
                }}
                .stat-card {{
                    flex: 1;
                    background: #f8f9fa;
                    padding: 20px;
                    border-radius: 8px;
                    text-align: center;
                }}
                .stat-number {{
                    font-size: 2em;
                    font-weight: bold;
                    color: #667eea;
                }}
                .positive {{ color: #28a745; }}
                .negative {{ color: #dc3545; }}
                .neutral {{ color: #6c757d; }}
                .section {{
                    margin: 30px 0;
                }}
                .section h3 {{
                    color: #333;
                    border-bottom: 2px solid #667eea;
                    padding-bottom: 10px;
                }}
                .theme-list {{
                    list-style: none;
                    padding: 0;
                }}
                .theme-list li {{
                    background: #f8f9fa;
                    margin: 10px 0;
                    padding: 15px;
                    border-left: 4px solid #667eea;
                    border-radius: 0 4px 4px 0;
                }}
                .cta-button {{
                    display: inline-block;
                    background: #667eea;
                    color: white;
                    padding: 15px 30px;
                    text-decoration: none;
                    border-radius: 5px;
                    margin: 20px 0;
                }}
                .footer {{
                    text-align: center;
                    margin-top: 40px;
                    color: #6c757d;
                    font-size: 0.9em;
                }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>📊 ReviewPulse Report</h1>
                <p>Review Summary for {business_name}</p>
                <p>{datetime.now().strftime('%B %d, %Y')}</p>
            </div>
            
            <div class="content">
                <div class="section">
                    <h2>📈 Review Overview</h2>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number">{total}</div>
                            <div>Total Reviews</div>
                        </div>
                        <div class="stat-card positive">
                            <div class="stat-number">{positive_pct}%</div>
                            <div>Positive</div>
                        </div>
                        <div class="stat-card negative">
                            <div class="stat-number">{negative_pct}%</div>
                            <div>Negative</div>
                        </div>
                        <div class="stat-card neutral">
                            <div class="stat-number">{neutral_pct}%</div>
                            <div>Neutral</div>
                        </div>
                    </div>
                </div>
                
                <div class="section">
                    <h3>✨ What Customers Love</h3>
                    {positive_themes_html}
                </div>
                
                <div class="section">
                    <h3>⚠️ Areas of Concern</h3>
                    {negative_themes_html}
                </div>
                
                <div class="section">
                    <h3>💡 Recommended Actions</h3>
                    {improvements_html}
                </div>
                
                <div class="section" style="text-align: center;">
                    <a href="{dashboard_url}" class="cta-button">View Full Dashboard</a>
                </div>
            </div>
            
            <div class="footer">
                <p>This report was generated by ReviewPulse</p>
                <p>© 2024 ReviewPulse. All rights reserved.</p>
            </div>
        </body>
        </html>
        """
        
        return html_content
    
    def _generate_theme_list(self, themes: List[str]) -> str:
        """Generate HTML list for themes"""
        if not themes:
            return "<p>No specific themes identified in this category.</p>"
        
        list_items = "".join([f"<li>{theme}</li>" for theme in themes])
        return f'<ul class="theme-list">{list_items}</ul>'