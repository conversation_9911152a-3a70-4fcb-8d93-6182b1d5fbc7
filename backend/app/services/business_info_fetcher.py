"""
Business information fetcher using Google Places API
"""
import requests
from typing import Optional, Dict, Any
from app.config import settings
from app.models import BusinessSearchResponse


class BusinessInfoFetcher:
    """Fetch detailed business information from Google Places API"""
    
    def __init__(self):
        self.api_key = settings.google_places_api_key
        self.base_url = "https://maps.googleapis.com/maps/api/place"
    
    async def get_place_details(self, place_id: str) -> Optional[BusinessSearchResponse]:
        """
        Get detailed information about a place using Place ID
        """
        print(f"🔍 BusinessInfoFetcher: Getting details for Place ID: {place_id}")
        
        if not self.api_key:
            print(f"❌ Google Places API key not configured!")
            raise ValueError("Google Places API key not configured")
        
        print(f"🔑 API Key available: {self.api_key[:20]}...")
        url = f"{self.base_url}/details/json"
        print(f"📡 Request URL: {url}")
        
        # Request specific fields to get comprehensive business info
        fields = [
            "place_id",
            "name", 
            "formatted_address",
            "formatted_phone_number",
            "international_phone_number",
            "website",
            "rating",
            "user_ratings_total",
            "types",
            "business_status",
            "opening_hours",
            "geometry"
        ]
        
        params = {
            'place_id': place_id,
            'fields': ','.join(fields),
            'key': self.api_key
        }
        
        print(f"📋 Request params: place_id={place_id}, fields={','.join(fields)}")
        
        try:
            print(f"📡 Making request to Google Places API...")
            response = requests.get(url, params=params, timeout=10)
            print(f"📊 Response status: {response.status_code}")
            response.raise_for_status()
            
            data = response.json()
            print(f"📄 Response status from API: {data.get('status')}")
            
            if data.get('status') != 'OK':
                error_msg = data.get('error_message', 'Unknown error')
                print(f"❌ Places API error: {data.get('status')} - {error_msg}")
                print(f"📄 Full response: {data}")
                return None
            
            result = data.get('result', {})
            print(f"✅ Got place details: {result.get('name', 'Unknown')}")
            
            # Parse and return structured business info
            return BusinessSearchResponse(
                place_id=result.get('place_id', place_id),
                name=result.get('name', 'Unknown Business'),
                address=result.get('formatted_address'),
                phone=result.get('formatted_phone_number') or result.get('international_phone_number'),
                website=result.get('website'),
                rating=result.get('rating'),
                total_reviews=result.get('user_ratings_total'),
                types=result.get('types', [])
            )
            
        except requests.exceptions.RequestException as e:
            print(f"Error fetching place details: {e}")
            return None
        except Exception as e:
            print(f"Unexpected error: {e}")
            return None
    
    async def search_places_by_text(self, query: str, location: Optional[str] = None) -> list[BusinessSearchResponse]:
        """
        Search for places using text query
        """
        if not self.api_key:
            raise ValueError("Google Places API key not configured")
        
        url = f"{self.base_url}/textsearch/json"
        
        params = {
            'query': query,
            'key': self.api_key
        }
        
        if location:
            params['location'] = location
        
        try:
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('status') != 'OK':
                print(f"Places API error: {data.get('status')} - {data.get('error_message', '')}")
                return []
            
            results = []
            for result in data.get('results', [])[:5]:  # Limit to top 5 results
                business_info = BusinessSearchResponse(
                    place_id=result.get('place_id'),
                    name=result.get('name', 'Unknown Business'),
                    address=result.get('formatted_address'),
                    rating=result.get('rating'),
                    total_reviews=result.get('user_ratings_total'),
                    types=result.get('types', [])
                )
                results.append(business_info)
            
            return results
            
        except requests.exceptions.RequestException as e:
            print(f"Error searching places: {e}")
            return []
        except Exception as e:
            print(f"Unexpected error: {e}")
            return []
    
    async def find_place_nearby(self, lat: float, lng: float, radius: int = 50) -> list[BusinessSearchResponse]:
        """
        Find places near specific coordinates
        """
        if not self.api_key:
            raise ValueError("Google Places API key not configured")
        
        url = f"{self.base_url}/nearbysearch/json"
        
        params = {
            'location': f"{lat},{lng}",
            'radius': radius,
            'key': self.api_key
        }
        
        try:
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            
            if data.get('status') != 'OK':
                print(f"Places API error: {data.get('status')} - {data.get('error_message', '')}")
                return []
            
            results = []
            for result in data.get('results', [])[:5]:  # Limit to top 5 results
                business_info = BusinessSearchResponse(
                    place_id=result.get('place_id'),
                    name=result.get('name', 'Unknown Business'),
                    address=result.get('vicinity'),  # nearby search uses 'vicinity' instead of 'formatted_address'
                    rating=result.get('rating'),
                    total_reviews=result.get('user_ratings_total'),
                    types=result.get('types', [])
                )
                results.append(business_info)
            
            return results
            
        except requests.exceptions.RequestException as e:
            print(f"Error finding nearby places: {e}")
            return []
        except Exception as e:
            print(f"Unexpected error: {e}")
            return []
    
    def validate_business_info(self, business_info: BusinessSearchResponse) -> bool:
        """
        Validate that the business info contains minimum required data
        """
        if not business_info.place_id or not business_info.name:
            return False
        
        # Check if it's likely a business (not just a geographic location)
        business_types = [
            'establishment', 'store', 'restaurant', 'food', 'lodging', 
            'shopping_mall', 'bank', 'hospital', 'school', 'gym',
            'beauty_salon', 'car_dealer', 'gas_station', 'pharmacy'
        ]
        
        if business_info.types:
            has_business_type = any(btype in business_info.types for btype in business_types)
            if not has_business_type:
                # If no clear business type, check if it has reviews (indicates it's a reviewable business)
                return business_info.total_reviews and business_info.total_reviews > 0
        
        return True