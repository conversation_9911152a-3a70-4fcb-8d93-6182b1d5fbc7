from typing import Dict, Any
from app.config import settings

class SentimentAnalysisService:
    def __init__(self):
        self.use_azure = bool(settings.azure_text_analytics_key and settings.azure_text_analytics_endpoint)
        self.use_gemini = bool(settings.gemini_api_key)
        
        if not self.use_azure and not self.use_gemini:
            raise ValueError("No AI service configured for sentiment analysis")
    
    async def analyze_sentiment(self, text: str) -> Dict[str, Any]:
        """Analyze sentiment of given text"""
        if self.use_azure:
            return await self._analyze_with_azure(text)
        elif self.use_gemini:
            return await self._analyze_with_gemini(text)
        else:
            raise ValueError("No sentiment analysis service available")
    
    async def _analyze_with_azure(self, text: str) -> Dict[str, Any]:
        """Analyze sentiment using Azure Cognitive Services"""
        try:
            from azure.ai.textanalytics import TextAnalyticsClient
            from azure.core.credentials import AzureKeyCredential
            
            credential = AzureKeyCredential(settings.azure_text_analytics_key)
            client = TextAnalyticsClient(
                endpoint=settings.azure_text_analytics_endpoint,
                credential=credential
            )
            
            documents = [text]
            response = client.analyze_sentiment(documents=documents)[0]
            
            # Map Azure sentiment to our enum
            sentiment_mapping = {
                "positive": "positive",
                "negative": "negative", 
                "neutral": "neutral"
            }
            
            sentiment = sentiment_mapping.get(response.sentiment.lower(), "neutral")
            confidence = max(
                response.confidence_scores.positive,
                response.confidence_scores.negative,
                response.confidence_scores.neutral
            )
            
            return {
                "sentiment": sentiment,
                "confidence": confidence
            }
            
        except Exception as e:
            # Fallback to simple rule-based analysis
            return self._fallback_sentiment_analysis(text)
    
    async def _analyze_with_gemini(self, text: str) -> Dict[str, Any]:
        """Analyze sentiment using Google Gemini"""
        try:
            import google.generativeai as genai
            
            genai.configure(api_key=settings.gemini_api_key)
            model = genai.GenerativeModel('gemini-1.5-flash')
            
            prompt = f"""Analyze the sentiment of the following text and respond with only 'positive', 'negative', or 'neutral'.

Text: {text}

Sentiment:"""
            
            response = model.generate_content(prompt)
            sentiment = response.text.strip().lower()
            
            # Validate response
            if sentiment not in ["positive", "negative", "neutral"]:
                sentiment = "neutral"
            
            return {
                "sentiment": sentiment,
                "confidence": 0.85  # Default confidence for Gemini
            }
            
        except Exception as e:
            # Fallback to simple rule-based analysis
            return self._fallback_sentiment_analysis(text)
    
    def _fallback_sentiment_analysis(self, text: str) -> Dict[str, Any]:
        """Simple rule-based sentiment analysis as fallback"""
        positive_words = [
            "good", "great", "excellent", "amazing", "wonderful", "fantastic",
            "love", "best", "perfect", "awesome", "outstanding", "brilliant"
        ]
        
        negative_words = [
            "bad", "terrible", "awful", "horrible", "worst", "hate",
            "disappointing", "poor", "pathetic", "disgusting", "rude"
        ]
        
        text_lower = text.lower()
        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)
        
        if positive_count > negative_count:
            return {"sentiment": "positive", "confidence": 0.6}
        elif negative_count > positive_count:
            return {"sentiment": "negative", "confidence": 0.6}
        else:
            return {"sentiment": "neutral", "confidence": 0.5}