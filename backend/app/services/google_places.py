import httpx
from typing import List, Dict, Any
from datetime import datetime
from app.config import settings

class GooglePlacesService:
    def __init__(self):
        self.api_key = settings.google_places_api_key
        self.base_url = "https://maps.googleapis.com/maps/api/place"
    
    async def fetch_reviews(self, place_id: str) -> List[Dict[str, Any]]:
        """Fetch reviews for a given Google Place ID"""
        if not self.api_key:
            raise ValueError("Google Places API key not configured")
        
        url = f"{self.base_url}/details/json"
        params = {
            "place_id": place_id,
            "fields": "reviews,name",
            "key": self.api_key
        }
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(url, params=params)
                response.raise_for_status()
                data = response.json()
                
                if data.get("status") != "OK":
                    raise Exception(f"Google Places API error: {data.get('status')}")
                
                reviews = data.get("result", {}).get("reviews", [])
                processed_reviews = []
                
                for review in reviews:
                    processed_review = {
                        "google_review_id": f"{place_id}_{review.get('time', 0)}_{hash(review.get('text', ''))}",
                        "review_text": review.get("text", ""),
                        "rating": review.get("rating", 0),
                        "author_name": review.get("author_name", "Anonymous"),
                        "review_date": datetime.fromtimestamp(review.get("time", 0)).isoformat(),
                    }
                    processed_reviews.append(processed_review)
                
                return processed_reviews
                
            except httpx.HTTPError as e:
                raise Exception(f"HTTP error fetching reviews: {str(e)}")
            except Exception as e:
                raise Exception(f"Error fetching reviews: {str(e)}")
    
    async def search_place(self, business_name: str, location: str = None) -> Dict[str, Any]:
        """Search for a place by business name and location"""
        if not self.api_key:
            raise ValueError("Google Places API key not configured")
        
        url = f"{self.base_url}/findplacefromtext/json"
        query = business_name
        if location:
            query += f" {location}"
            
        params = {
            "input": query,
            "inputtype": "textquery",
            "fields": "place_id,name,formatted_address",
            "key": self.api_key
        }
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(url, params=params)
                response.raise_for_status()
                data = response.json()
                
                if data.get("status") != "OK":
                    raise Exception(f"Google Places API error: {data.get('status')}")
                
                candidates = data.get("candidates", [])
                if not candidates:
                    raise Exception("No places found matching the search criteria")
                
                return candidates[0]  # Return the first match
                
            except httpx.HTTPError as e:
                raise Exception(f"HTTP error searching place: {str(e)}")
            except Exception as e:
                raise Exception(f"Error searching place: {str(e)}")