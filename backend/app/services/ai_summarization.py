from typing import List, Dict, Any
from app.config import settings

class AISummarizationService:
    def __init__(self):
        self.use_azure = bool(settings.azure_text_analytics_key and settings.azure_text_analytics_endpoint)
        self.use_gemini = bool(settings.gemini_api_key)
        
        if not self.use_azure and not self.use_gemini:
            raise ValueError("No AI service configured for summarization")
    
    async def generate_summary(self, reviews: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate AI summary from reviews"""
        if not reviews:
            return {
                "positive_themes": [],
                "negative_themes": [],
                "recommended_improvements": []
            }
        
        # Separate reviews by sentiment
        positive_reviews = [r for r in reviews if r.get("sentiment") == "positive"]
        negative_reviews = [r for r in reviews if r.get("sentiment") == "negative"]
        
        if self.use_gemini:
            return await self._generate_with_gemini(positive_reviews, negative_reviews)
        else:
            return self._fallback_summary(positive_reviews, negative_reviews)
    
    async def _generate_with_gemini(self, positive_reviews: List[Dict], negative_reviews: List[Dict]) -> Dict[str, Any]:
        """Generate summary using Google Gemini"""
        try:
            import google.generativeai as genai
            import json
            
            genai.configure(api_key=settings.gemini_api_key)
            model = genai.GenerativeModel('gemini-1.5-flash')
            
            # Prepare review texts
            positive_texts = [r["review_text"] for r in positive_reviews[:20]]  # Limit to avoid token limits
            negative_texts = [r["review_text"] for r in negative_reviews[:20]]
            
            prompt = f"""
            Analyze the following customer reviews and provide a structured summary:

            POSITIVE REVIEWS:
            {chr(10).join(positive_texts[:10])}

            NEGATIVE REVIEWS:
            {chr(10).join(negative_texts[:10])}

            Please provide a JSON response with exactly this structure:
            {{
                "positive_themes": ["theme1", "theme2", "theme3"],
                "negative_themes": ["theme1", "theme2", "theme3"],
                "recommended_improvements": ["improvement1", "improvement2", "improvement3"]
            }}

            Keep each theme and improvement to 1-2 sentences maximum.
            """
            
            response = model.generate_content(prompt)
            content = response.text
            
            # Try to parse JSON response
            try:
                summary_data = json.loads(content)
                return {
                    "positive_themes": summary_data.get("positive_themes", [])[:5],
                    "negative_themes": summary_data.get("negative_themes", [])[:5],
                    "recommended_improvements": summary_data.get("recommended_improvements", [])[:5]
                }
            except json.JSONDecodeError:
                # Fallback if JSON parsing fails
                return self._fallback_summary(positive_reviews, negative_reviews)
            
        except Exception as e:
            return self._fallback_summary(positive_reviews, negative_reviews)
    
    def _fallback_summary(self, positive_reviews: List[Dict], negative_reviews: List[Dict]) -> Dict[str, Any]:
        """Simple rule-based summary as fallback"""
        positive_themes = []
        negative_themes = []
        improvements = []
        
        # Analyze positive reviews
        if positive_reviews:
            positive_keywords = self._extract_keywords([r["review_text"] for r in positive_reviews])
            positive_themes = [
                f"Customers appreciate {keyword}" for keyword in positive_keywords[:3]
            ]
        
        # Analyze negative reviews
        if negative_reviews:
            negative_keywords = self._extract_keywords([r["review_text"] for r in negative_reviews])
            negative_themes = [
                f"Customers complain about {keyword}" for keyword in negative_keywords[:3]
            ]
            improvements = [
                f"Consider improving {keyword}" for keyword in negative_keywords[:3]
            ]
        
        return {
            "positive_themes": positive_themes,
            "negative_themes": negative_themes,
            "recommended_improvements": improvements
        }
    
    def _extract_keywords(self, texts: List[str]) -> List[str]:
        """Extract common keywords from texts"""
        # Simple keyword extraction
        common_words = set(['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'])
        word_counts = {}
        
        for text in texts:
            words = text.lower().split()
            for word in words:
                clean_word = word.strip('.,!?();:"')
                if len(clean_word) > 3 and clean_word not in common_words:
                    word_counts[clean_word] = word_counts.get(clean_word, 0) + 1
        
        # Return top words
        sorted_words = sorted(word_counts.items(), key=lambda x: x[1], reverse=True)
        return [word for word, count in sorted_words[:5]]