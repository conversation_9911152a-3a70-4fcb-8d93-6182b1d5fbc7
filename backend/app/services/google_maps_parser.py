"""
Google Maps URL parser to extract Place IDs and coordinates
"""
import re
import urllib.parse as urlparse
from typing import Optional, Dict, Any
import requests
from app.config import settings


class GoogleMapsParser:
    """Parser for Google Maps URLs to extract place information"""
    
    def __init__(self):
        self.api_key = settings.google_places_api_key
    
    def extract_place_info_from_url(self, url: str) -> Optional[Dict[str, Any]]:
        """
        Extract place information from various Google Maps URL formats
        Returns dict with place_id, coordinates, or None if not found
        """
        print(f"🔍 GoogleMapsParser: Starting URL extraction for: {url}")
        
        # First, resolve shortened URLs
        resolved_url = self._resolve_shortened_url(url)
        if resolved_url != url:
            print(f"🔗 Resolved shortened URL: {url} -> {resolved_url}")
            url = resolved_url
        
        print(f"📍 Parsing URL: {url}")
        
        # Method 1: Look for place_id parameter
        print("🔍 Method 1: Looking for place_id parameter...")
        place_id = self._extract_place_id_from_url(url)
        if place_id:
            print(f"✅ Found place_id: {place_id}")
            return {"place_id": place_id, "method": "place_id"}
        
        # Method 2: Look for coordinates in URL
        print("🔍 Method 2: Looking for coordinates...")
        coords = self._extract_coordinates_from_url(url)
        if coords:
            print(f"✅ Found coordinates: {coords}")
            return {"coordinates": coords, "method": "coordinates"}
        
        # Method 3: Look for CID (Customer ID) format
        print("🔍 Method 3: Looking for CID format...")
        cid = self._extract_cid_from_url(url)
        if cid:
            print(f"✅ Found CID: {cid}")
            return {"cid": cid, "method": "cid"}
        
        print("❌ No place information found in URL")
        return None
    
    def _resolve_shortened_url(self, url: str) -> str:
        """Resolve shortened URLs like maps.app.goo.gl to full URLs"""
        if not any(domain in url for domain in ['goo.gl', 'maps.app.goo.gl', 'g.co']):
            return url
        
        print(f"🔗 Resolving shortened URL: {url}")
        try:
            # Make a HEAD request to get the redirect without downloading content
            response = requests.head(url, allow_redirects=True, timeout=10)
            final_url = response.url
            print(f"✅ Resolved to: {final_url}")
            return final_url
        except Exception as e:
            print(f"❌ Error resolving shortened URL: {e}")
            return url
    
    def _extract_place_id_from_url(self, url: str) -> Optional[str]:
        """Extract Place ID from URL parameters"""
        parsed = urlparse.urlparse(url)
        query_params = urlparse.parse_qs(parsed.query)
        
        # Check for place_id in query parameters
        if 'place_id' in query_params:
            return query_params['place_id'][0]
        
        # Check for place_id in the path or fragment
        place_id_pattern = r'ChIJ[\w-]+'
        match = re.search(place_id_pattern, url)
        if match:
            return match.group(0)
        
        return None
    
    def _extract_coordinates_from_url(self, url: str) -> Optional[Dict[str, float]]:
        """Extract coordinates from Google Maps URL"""
        # Pattern for @lat,lng format
        coord_pattern = r'@(-?\d+\.?\d*),(-?\d+\.?\d*)'
        match = re.search(coord_pattern, url)
        if match:
            return {
                "lat": float(match.group(1)),
                "lng": float(match.group(2))
            }
        
        # Pattern for ll= parameter
        ll_pattern = r'll=(-?\d+\.?\d*),(-?\d+\.?\d*)'
        match = re.search(ll_pattern, url)
        if match:
            return {
                "lat": float(match.group(1)),
                "lng": float(match.group(2))
            }
        
        return None
    
    def _extract_cid_from_url(self, url: str) -> Optional[str]:
        """Extract CID (Customer ID) from URL"""
        # Pattern like: 0x40887f00435c068b:0xd50aa7303a776809
        cid_pattern = r'0x[0-9a-f]+:0x([0-9a-f]+)'
        match = re.search(cid_pattern, url, re.IGNORECASE)
        if match:
            cid_hex = match.group(1)
            cid_decimal = str(int(cid_hex, 16))
            return cid_decimal
        
        # Pattern for ftid
        ftid_pattern = r'!1s0x[0-9a-f]+:0x([0-9a-f]+)'
        match = re.search(ftid_pattern, url, re.IGNORECASE)
        if match:
            ftid_hex = match.group(1)
            ftid_decimal = str(int(ftid_hex, 16))
            return ftid_decimal
        
        return None
    
    async def resolve_to_place_id(self, url_info: Dict[str, Any]) -> Optional[str]:
        """
        Resolve URL info to a Google Place ID using Google Places API
        """
        if url_info.get("method") == "place_id":
            return url_info["place_id"]
        
        elif url_info.get("method") == "coordinates":
            return await self._find_place_by_coordinates(url_info["coordinates"])
        
        elif url_info.get("method") == "cid":
            # CID resolution is more complex, try nearby search with known location
            return None
        
        return None
    
    async def _find_place_by_coordinates(self, coords: Dict[str, float]) -> Optional[str]:
        """Find place ID using coordinates with nearby search"""
        if not self.api_key:
            return None
        
        url = "https://maps.googleapis.com/maps/api/place/nearbysearch/json"
        params = {
            'location': f"{coords['lat']},{coords['lng']}",
            'radius': 50,  # 50 meter radius
            'key': self.api_key
        }
        
        try:
            response = requests.get(url, params=params, timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get('results') and len(data['results']) > 0:
                    # Return the first (closest) result
                    return data['results'][0]['place_id']
        except Exception as e:
            print(f"Error finding place by coordinates: {e}")
        
        return None
    
    def get_example_urls(self) -> Dict[str, str]:
        """Get example URLs for testing"""
        return {
            "place_id": "https://maps.google.com/maps?place_id=ChIJKxDbe_lYwokRVf__s8CPn-o",
            "coordinates": "https://www.google.com/maps/@41.361552,36.234097,15z",
            "cid": "https://www.google.com/maps/place//data=!4m2!3m1!1s0x40887f00435c068b:0xd50aa7303a776809",
            "share_link": "https://maps.app.goo.gl/xyz123"
        }