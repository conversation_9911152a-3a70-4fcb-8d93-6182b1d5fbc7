import os
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    # Supabase
    supabase_url: str = os.getenv("SUPABASE_URL", "")
    supabase_anon_key: str = os.getenv("SUPABASE_ANON_KEY", "")
    supabase_service_role_key: str = os.getenv("SUPABASE_SERVICE_ROLE_KEY", "")
    
    # Google Places API
    google_places_api_key: str = os.getenv("GOOGLE_PLACES_API_KEY", "")
    
    # Azure Cognitive Services
    azure_text_analytics_endpoint: str = os.getenv("AZURE_TEXT_ANALYTICS_ENDPOINT", "")
    azure_text_analytics_key: str = os.getenv("AZURE_TEXT_ANALYTICS_KEY", "")
    
    # Google Gemini
    gemini_api_key: str = os.getenv("GEMINI_API_KEY", "")
    
    # Email
    resend_api_key: str = os.getenv("RESEND_API_KEY", "")
    
    # JWT
    secret_key: str = os.getenv("SECRET_KEY", "fallback-secret-key")
    algorithm: str = os.getenv("ALGORITHM", "HS256")
    access_token_expire_minutes: int = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))
    
    class Config:
        env_file = ".env"

settings = Settings()