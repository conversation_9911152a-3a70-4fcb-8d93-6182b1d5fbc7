-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- <PERSON>reate custom types
CREATE TYPE sentiment_type AS ENUM ('positive', 'negative', 'neutral');

-- Users table (handled by <PERSON><PERSON><PERSON> Auth)
-- We'll reference auth.users for the user_id

-- Businesses table (shared across all users)
CREATE TABLE businesses (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    google_place_id VARCHAR UNIQUE NOT NULL,
    business_name VARCHAR NOT NULL,
    business_address TEXT,
    phone_number VARCHAR,
    website VARCHAR,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Business profiles table (user's saved businesses)
CREATE TABLE business_profiles (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    business_id UUID REFERENCES businesses(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, business_id)
);

-- Reviews table
CREATE TABLE reviews (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    google_place_id VARCHAR NOT NULL,
    google_review_id VARCHAR UNIQUE NOT NULL,
    review_text TEXT NOT NULL,
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    author_name VARCHAR,
    review_date TIMESTAMP WITH TIME ZONE NOT NULL,
    sentiment sentiment_type,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Summaries table
CREATE TABLE summaries (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    positive_themes JSONB DEFAULT '[]'::jsonb,
    negative_themes JSONB DEFAULT '[]'::jsonb,
    recommended_improvements JSONB DEFAULT '[]'::jsonb,
    total_reviews INTEGER DEFAULT 0,
    sentiment_distribution JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Email reports table (for logging analysis instead of sending emails)
CREATE TABLE email_reports (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    business_name VARCHAR NOT NULL,
    recipient_email VARCHAR NOT NULL,
    analysis_data JSONB NOT NULL,
    report_type VARCHAR DEFAULT 'email_summary',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_reviews_user_id ON reviews(user_id);
CREATE INDEX idx_reviews_google_place_id ON reviews(google_place_id);
CREATE INDEX idx_reviews_review_date ON reviews(review_date);
CREATE INDEX idx_reviews_sentiment ON reviews(sentiment);
CREATE INDEX idx_summaries_user_id ON summaries(user_id);
CREATE INDEX idx_summaries_period ON summaries(period_start, period_end);
CREATE INDEX idx_businesses_google_place_id ON businesses(google_place_id);
CREATE INDEX idx_business_profiles_user_id ON business_profiles(user_id);
CREATE INDEX idx_business_profiles_business_id ON business_profiles(business_id);
CREATE INDEX idx_email_reports_user_id ON email_reports(user_id);
CREATE INDEX idx_email_reports_created_at ON email_reports(created_at);

-- Row Level Security (RLS) policies
ALTER TABLE businesses ENABLE ROW LEVEL SECURITY;
ALTER TABLE business_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE summaries ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_reports ENABLE ROW LEVEL SECURITY;

-- Policies for businesses (all users can read)
CREATE POLICY "Anyone can view businesses" ON businesses
    FOR SELECT USING (true);

CREATE POLICY "Anyone can insert businesses" ON businesses
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Anyone can update businesses" ON businesses
    FOR UPDATE USING (true);

-- Policies for business_profiles
CREATE POLICY "Users can view their own business profiles" ON business_profiles
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own business profiles" ON business_profiles
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own business profiles" ON business_profiles
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own business profiles" ON business_profiles
    FOR DELETE USING (auth.uid() = user_id);

-- Policies for reviews
CREATE POLICY "Users can view their own reviews" ON reviews
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own reviews" ON reviews
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own reviews" ON reviews
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own reviews" ON reviews
    FOR DELETE USING (auth.uid() = user_id);

-- Policies for summaries
CREATE POLICY "Users can view their own summaries" ON summaries
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own summaries" ON summaries
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own summaries" ON summaries
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own summaries" ON summaries
    FOR DELETE USING (auth.uid() = user_id);

-- Policies for email_reports
CREATE POLICY "Users can view their own email reports" ON email_reports
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own email reports" ON email_reports
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own email reports" ON email_reports
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own email reports" ON email_reports
    FOR DELETE USING (auth.uid() = user_id);

-- Functions for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_businesses_updated_at BEFORE UPDATE ON businesses 
    FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_business_profiles_updated_at BEFORE UPDATE ON business_profiles 
    FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_reviews_updated_at BEFORE UPDATE ON reviews 
    FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_summaries_updated_at BEFORE UPDATE ON summaries 
    FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

CREATE TRIGGER update_email_reports_updated_at BEFORE UPDATE ON email_reports 
    FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();