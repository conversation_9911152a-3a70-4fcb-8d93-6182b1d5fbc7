#!/usr/bin/env python3
"""
Find Google Place ID using coordinates and address
"""
import requests
import json
import os

def find_place_id_by_coordinates(lat, lng, api_key):
    """Find Place ID using coordinates with nearby search"""
    url = "https://maps.googleapis.com/maps/api/place/nearbysearch/json"
    
    params = {
        'location': f"{lat},{lng}",
        'radius': 50,  # 50 meter radius
        'key': api_key
    }
    
    response = requests.get(url, params=params)
    if response.status_code == 200:
        data = response.json()
        return data
    else:
        print(f"Error: {response.status_code} - {response.text}")
        return None

def find_place_id_by_text(query, api_key):
    """Find Place ID using text search"""
    url = "https://maps.googleapis.com/maps/api/place/textsearch/json"
    
    params = {
        'query': query,
        'key': api_key
    }
    
    response = requests.get(url, params=params)
    if response.status_code == 200:
        data = response.json()
        return data
    else:
        print(f"Error: {response.status_code} - {response.text}")
        return None

if __name__ == "__main__":
    # Get API key from environment or user input
    api_key = os.getenv('GOOGLE_PLACES_API_KEY')
    if not api_key:
        api_key = input("Enter your Google Places API key: ")
    
    # Location details
    lat = 41.361552
    lng = 36.234097
    address = "Adnan Menderes Blv. 9-53,Körfez, 55270 Atakum/Samsun, Türkiye"
    
    print(f"Searching for Place ID near coordinates: {lat}, {lng}")
    print(f"Address: {address}")
    print("=" * 50)
    
    # Method 1: Search by coordinates
    print("\n1. Searching by coordinates...")
    result = find_place_id_by_coordinates(lat, lng, api_key)
    
    if result and 'results' in result and len(result['results']) > 0:
        print(f"Found {len(result['results'])} places near coordinates:")
        for i, place in enumerate(result['results'][:5]):  # Show first 5 results
            print(f"  {i+1}. {place['name']}")
            print(f"     Place ID: {place['place_id']}")
            print(f"     Address: {place.get('vicinity', 'N/A')}")
            print(f"     Types: {', '.join(place.get('types', []))}")
            print()
    
    # Method 2: Search by address text
    print("\n2. Searching by address text...")
    result = find_place_id_by_text(address, api_key)
    
    if result and 'results' in result and len(result['results']) > 0:
        print(f"Found {len(result['results'])} places for address search:")
        for i, place in enumerate(result['results'][:3]):  # Show first 3 results
            print(f"  {i+1}. {place['name']}")
            print(f"     Place ID: {place['place_id']}")
            print(f"     Address: {place.get('formatted_address', 'N/A')}")
            print(f"     Types: {', '.join(place.get('types', []))}")
            print()
    
    # Method 3: Search by a more specific query
    print("\n3. Searching by location name...")
    location_query = "Körfez Atakum Samsun Türkiye"
    result = find_place_id_by_text(location_query, api_key)
    
    if result and 'results' in result and len(result['results']) > 0:
        print(f"Found {len(result['results'])} places for location search:")
        for i, place in enumerate(result['results'][:3]):
            print(f"  {i+1}. {place['name']}")
            print(f"     Place ID: {place['place_id']}")
            print(f"     Address: {place.get('formatted_address', 'N/A')}")
            print(f"     Types: {', '.join(place.get('types', []))}")
            print()