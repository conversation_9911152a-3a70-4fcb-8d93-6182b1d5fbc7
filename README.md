# ReviewPulse - AI-Powered Review Analytics

ReviewPulse is a SaaS MVP for SMEs that aggregates Google Reviews, applies AI-powered sentiment analysis, and generates actionable weekly/monthly summaries.

## 🚀 Quick Start

### Backend Setup

1. Navigate to the backend directory:
   ```bash
   cd backend
   ```

2. Copy environment variables:
   ```bash
   cp .env.example .env
   ```

3. Configure your API keys in `.env`:
   - Supabase credentials (URL, anon key, service role key)
   - Google Places API key
   - AI service (Azure or OpenAI)
   - Resend API key for emails

4. Start the server:
   ```bash
   ./start.sh
   ```

The API will be available at `http://localhost:8000` with interactive docs at `/docs`.

## 🏗️ Tech Stack

| Layer | Technology |
|-------|------------|
| **Backend** | Python + FastAPI |
| **Database** | Supabase PostgreSQL |
| **Auth** | Supabase Auth |
| **AI Services** | Azure Cognitive Services OR OpenAI GPT-4o |
| **Reviews API** | Google Places API |
| **Email** | Resend API |

## 📊 Features

- ✅ User authentication (email/password + social logins)
- ✅ Google Reviews aggregation via Places API
- ✅ AI-powered sentiment analysis
- ✅ Automated AI summary generation
- ✅ Email report delivery
- ✅ REST API with OpenAPI documentation

## 🛠️ API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/auth/me` - Get current user

### Reviews
- `POST /api/reviews/fetch` - Fetch reviews from Google Places
- `GET /api/reviews/` - Get user's reviews with filters
- `GET /api/reviews/stats` - Get review statistics

### Summaries
- `POST /api/summaries/generate` - Generate AI summary
- `GET /api/summaries/` - Get user's summaries
- `GET /api/summaries/latest` - Get latest summary
- `POST /api/summaries/send-report` - Send email report

## 🗃️ Database Schema

The application uses the following main tables:
- `reviews` - Stores Google reviews with sentiment analysis
- `summaries` - Stores AI-generated summaries
- `business_profiles` - Stores business information

See `backend/database/schema.sql` for the complete schema.

## 🔧 Configuration

### Required Environment Variables

```bash
# Supabase
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Google Places API
GOOGLE_PLACES_API_KEY=your_google_places_api_key

# AI Services (choose one)
# Azure Cognitive Services
AZURE_TEXT_ANALYTICS_ENDPOINT=your_azure_endpoint
AZURE_TEXT_ANALYTICS_KEY=your_azure_key

# OpenAI (alternative)
OPENAI_API_KEY=your_openai_api_key

# Email Service
RESEND_API_KEY=your_resend_api_key

# App Configuration
SECRET_KEY=your_secret_key_for_jwt
```

## 🏃‍♂️ Development

### Install Dependencies
```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

### Run Development Server
```bash
uvicorn main:app --reload
```

### Database Setup
1. Create a new Supabase project
2. Run the SQL schema from `backend/database/schema.sql` in your Supabase SQL editor
3. Configure Row Level Security policies as needed

## 📈 Usage Flow

1. **User registers** via email/password or social login
2. **Fetch reviews** by providing a Google Place ID
3. **Automatic sentiment analysis** is applied to each review
4. **Generate AI summary** for any time period
5. **Send email reports** with charts and insights
6. **View data** through the REST API

## 🔐 Security

- JWT-based authentication
- Row Level Security (RLS) in Supabase
- API key authentication for external services
- Input validation and sanitization

## 📝 Future Enhancements

- Frontend dashboard with Next.js
- Scheduled report automation
- Multi-location business support
- Integration with other review platforms
- Advanced analytics and charts

## 📄 License

This project is for educational/demo purposes. See project requirements for detailed specifications.