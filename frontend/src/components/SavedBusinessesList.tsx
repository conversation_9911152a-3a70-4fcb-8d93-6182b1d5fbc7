'use client'

import { useEffect, useState } from 'react'
import { <PERSON><PERSON> } from './ui/button'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { Alert, AlertDescription } from './ui/alert'
import { Badge } from './ui/badge'
import { 
  Loader2, 
  MapPin, 
  Phone, 
  Globe, 
  Trash2,
  Download,
  Building2,
  Calendar
} from 'lucide-react'
import { businessApi, BusinessInfo } from '@/lib/api'

interface SavedBusinessesListProps {
  onFetchReviews: (businessId: string, businessName: string) => void
  refreshTrigger?: number // Optional prop to trigger refresh
}

export function SavedBusinessesList({ onFetchReviews, refreshTrigger }: SavedBusinessesListProps) {
  const [businesses, setBusinesses] = useState<BusinessInfo[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>({})

  const loadBusinesses = async () => {
    try {
      setIsLoading(true)
      setError(null)
      const data = await businessApi.getMyBusinesses()
      setBusinesses(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load businesses')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    loadBusinesses()
  }, [refreshTrigger])

  const handleRemoveBusiness = async (businessId: string) => {
    if (!confirm('Are you sure you want to remove this business from your saved list?')) {
      return
    }

    setLoadingStates(prev => ({ ...prev, [businessId]: true }))

    try {
      await businessApi.removeBusiness(businessId)
      setBusinesses(prev => prev.filter(b => b.id !== businessId))
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to remove business')
    } finally {
      setLoadingStates(prev => ({ ...prev, [businessId]: false }))
    }
  }

  const handleFetchReviews = (businessId: string, businessName: string) => {
    onFetchReviews(businessId, businessName)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          Loading your saved businesses...
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardContent className="py-6">
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <Button 
            onClick={loadBusinesses} 
            variant="outline" 
            size="sm" 
            className="mt-4"
          >
            Try Again
          </Button>
        </CardContent>
      </Card>
    )
  }

  if (businesses.length === 0) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <Building2 className="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium mb-2">No Businesses Saved</h3>
          <p className="text-gray-600 mb-4">
            Add a business using a Google Maps link to get started with review analysis.
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      <CardHeader className="px-0">
        <CardTitle className="flex items-center gap-2">
          <Building2 className="h-5 w-5" />
          Your Saved Businesses ({businesses.length})
        </CardTitle>
      </CardHeader>

      <div className="grid gap-4">
        {businesses.map((business) => (
          <Card key={business.id}>
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <h3 className="text-lg font-semibold truncate">
                    {business.business_name}
                  </h3>

                  {business.business_address && (
                    <div className="flex items-start gap-2 mt-2">
                      <MapPin className="h-4 w-4 mt-1 text-gray-500 flex-shrink-0" />
                      <span className="text-sm text-gray-700 break-words">
                        {business.business_address}
                      </span>
                    </div>
                  )}

                  <div className="flex flex-wrap items-center gap-4 mt-3">
                    {business.phone_number && (
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-gray-500" />
                        <span className="text-sm">{business.phone_number}</span>
                      </div>
                    )}

                    {business.website && (
                      <div className="flex items-center gap-2">
                        <Globe className="h-4 w-4 text-gray-500" />
                        <a 
                          href={business.website} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-sm text-blue-600 hover:underline"
                        >
                          Website
                        </a>
                      </div>
                    )}

                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-gray-500" />
                      <span className="text-sm text-gray-600">
                        Added {formatDate(business.created_at)}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="flex flex-col gap-2 ml-4">
                  <Button
                    size="sm"
                    onClick={() => handleFetchReviews(business.id, business.business_name)}
                    className="whitespace-nowrap"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Fetch Reviews
                  </Button>
                  
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleRemoveBusiness(business.id)}
                    disabled={loadingStates[business.id]}
                    className="whitespace-nowrap"
                  >
                    {loadingStates[business.id] ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Trash2 className="h-4 w-4 mr-2" />
                    )}
                    Remove
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}