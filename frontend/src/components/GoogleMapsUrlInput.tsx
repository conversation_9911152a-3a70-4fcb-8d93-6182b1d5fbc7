'use client'

import { useState } from 'react'
import { But<PERSON> } from './ui/button'
import { Input } from './ui/input'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { Alert, AlertDescription } from './ui/alert'
import { Loader2, MapPin, ExternalLink } from 'lucide-react'
import { businessApi, BusinessSearchResponse } from '@/lib/api'

interface GoogleMapsUrlInputProps {
  onBusinessFound: (business: BusinessSearchResponse) => void
}

export function GoogleMapsUrlInput({ onBusinessFound }: GoogleMapsUrlInputProps) {
  const [url, setUrl] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!url.trim()) return

    setIsLoading(true)
    setError(null)

    try {
      const business = await businessApi.searchFromUrl(url.trim())
      onBusinessFound(business)
      setUrl('') // Clear the input on success
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to extract business information')
    } finally {
      setIsLoading(false)
    }
  }

  const isValidGoogleMapsUrl = (url: string): boolean => {
    return url.includes('maps.google') || url.includes('google.com/maps') || url.includes('maps.app.goo.gl')
  }

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MapPin className="h-5 w-5" />
          Add Business from Google Maps
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="maps-url" className="text-sm font-medium">
              Google Maps URL
            </label>
            <Input
              id="maps-url"
              type="url"
              placeholder="https://maps.google.com/maps?place_id=..."
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              disabled={isLoading}
              className="w-full"
            />
            <p className="text-xs text-gray-600">
              Paste a Google Maps link to a business, restaurant, or any reviewable location
            </p>
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <Button
            type="submit"
            disabled={!url.trim() || isLoading || !isValidGoogleMapsUrl(url)}
            className="w-full"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Extracting Business Info...
              </>
            ) : (
              <>
                <ExternalLink className="mr-2 h-4 w-4" />
                Extract Business Information
              </>
            )}
          </Button>
        </form>

        <div className="mt-4 p-3 bg-gray-50 rounded-lg">
          <p className="text-sm font-medium mb-2">How to get a Google Maps link:</p>
          <ol className="text-xs text-gray-600 space-y-1">
            <li>1. Go to Google Maps and search for a business</li>
            <li>2. Click on the business to open its details</li>
            <li>3. Copy the URL from your browser's address bar</li>
            <li>4. Paste it here to automatically extract business information</li>
          </ol>
        </div>
      </CardContent>
    </Card>
  )
}