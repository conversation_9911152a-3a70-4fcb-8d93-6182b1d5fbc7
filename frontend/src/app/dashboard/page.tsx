'use client'

import { useState } from 'react'
import { useReviews, useFetchReviews, type Review } from '@/hooks/useReviews'
import { useSummaries } from '@/hooks/useSummaries'
import { useBusinesses } from '@/hooks/useBusinesses'
import { GoogleMapsUrlInput } from '@/components/GoogleMapsUrlInput'
import { BusinessConfirmationDialog } from '@/components/BusinessConfirmationDialog'
import { SavedBusinessesList } from '@/components/SavedBusinessesList'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { BusinessSearchResponse } from '@/lib/api'

export default function DashboardPage() {
  const [placeId, setPlaceId] = useState('')
  const [activePlaceId, setActivePlaceId] = useState<string>('')
  const [pendingBusiness, setPendingBusiness] = useState<BusinessSearchResponse | null>(null)
  const [refreshTrigger, setRefreshTrigger] = useState(0)
  
  const { data: reviewsData, isLoading: reviewsLoading } = useReviews(activePlaceId)
  const { data: summariesData } = useSummaries(activePlaceId)
  const fetchReviewsMutation = useFetchReviews()
  const { fetchReviewsByBusiness } = useBusinesses()

  const handleFetchReviews = async () => {
    if (!placeId.trim()) return
    
    setActivePlaceId(placeId)
    fetchReviewsMutation.mutate(placeId)
  }

  const handleBusinessFound = (business: BusinessSearchResponse) => {
    setPendingBusiness(business)
  }

  const handleBusinessConfirmed = (businessId: string) => {
    setPendingBusiness(null)
    setRefreshTrigger(prev => prev + 1) // Trigger refresh of saved businesses
  }

  const handleBusinessCancelled = () => {
    setPendingBusiness(null)
  }

  const handleFetchReviewsByBusiness = async (businessId: string, businessName: string) => {
    try {
      const result = await fetchReviewsByBusiness(businessId)
      // Show success message or update UI accordingly
      alert(`Successfully fetched ${result.total_reviews} reviews for ${result.business_name}`)
    } catch (error) {
      alert(`Failed to fetch reviews: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  const reviews = reviewsData?.reviews || []
  const totalReviews = reviewsData?.total || 0
  const averageRating = reviewsData?.average_rating || 0
  const positiveReviews = reviews.filter(r => r.sentiment === 'positive').length
  const positivePercentage = totalReviews > 0 ? Math.round((positiveReviews / totalReviews) * 100) : 0

  return (
    <div className="px-4 py-6 sm:px-0 max-w-7xl mx-auto">
      <h1 className="text-3xl font-bold text-gray-900 mb-8">
        Review Analytics Dashboard
      </h1>

      <Tabs defaultValue="businesses" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="businesses">My Businesses</TabsTrigger>
          <TabsTrigger value="add-business">Add Business</TabsTrigger>
          <TabsTrigger value="manual">Manual Entry</TabsTrigger>
        </TabsList>

        <TabsContent value="businesses" className="space-y-6">
          <SavedBusinessesList 
            onFetchReviews={handleFetchReviewsByBusiness}
            refreshTrigger={refreshTrigger}
          />
        </TabsContent>

        <TabsContent value="add-business" className="space-y-6">
          <div className="flex justify-center">
            <GoogleMapsUrlInput onBusinessFound={handleBusinessFound} />
          </div>
        </TabsContent>

        <TabsContent value="manual" className="space-y-6">
          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle>Manual Place ID Entry</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label htmlFor="place-id" className="block text-sm font-medium text-gray-700 mb-2">
                  Google Place ID
                </label>
                <input
                  type="text"
                  id="place-id"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                  placeholder="Enter Google Place ID"
                  value={placeId}
                  onChange={(e) => setPlaceId(e.target.value)}
                />
              </div>
              <button
                type="button"
                onClick={handleFetchReviews}
                disabled={fetchReviewsMutation.isPending || !placeId.trim()}
                className="w-full bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {fetchReviewsMutation.isPending ? 'Fetching...' : 'Fetch Reviews'}
              </button>
              {fetchReviewsMutation.error && (
                <Alert variant="destructive">
                  <AlertDescription>
                    Error: {fetchReviewsMutation.error.message}
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Analytics Section */}
      {(reviews.length > 0 || activePlaceId) && (
        <div className="mt-8 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-6">
                <h4 className="text-sm font-medium text-gray-500">Total Reviews</h4>
                <p className="text-2xl font-bold text-gray-900">{totalReviews}</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <h4 className="text-sm font-medium text-gray-500">Average Rating</h4>
                <p className="text-2xl font-bold text-gray-900">{averageRating.toFixed(1)}</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <h4 className="text-sm font-medium text-gray-500">Positive Sentiment</h4>
                <p className="text-2xl font-bold text-green-600">{positivePercentage}%</p>
              </CardContent>
            </Card>
          </div>

          {/* Reviews List */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Reviews</CardTitle>
            </CardHeader>
            <CardContent>
              {reviewsLoading ? (
                <div className="text-center text-gray-500">Loading reviews...</div>
              ) : reviews.length > 0 ? (
                <div className="space-y-4">
                  {reviews.slice(0, 5).map((review: Review) => (
                    <div key={review.id} className="border-b border-gray-200 pb-4 last:border-b-0">
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="font-medium text-gray-900">{review.author_name}</h4>
                        <div className="flex items-center space-x-2">
                          <span className="text-yellow-500">{'★'.repeat(review.rating)}</span>
                          {review.sentiment && (
                            <span className={`px-2 py-1 rounded-full text-xs ${
                              review.sentiment === 'positive' ? 'bg-green-100 text-green-800' :
                              review.sentiment === 'negative' ? 'bg-red-100 text-red-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {review.sentiment}
                            </span>
                          )}
                        </div>
                      </div>
                      <p className="text-gray-600 text-sm">{review.text}</p>
                      <p className="text-gray-400 text-xs mt-1">
                        {new Date(review.time).toLocaleDateString()}
                      </p>
                    </div>
                  ))}
                  {reviews.length > 5 && (
                    <p className="text-center text-gray-500 text-sm">
                      And {reviews.length - 5} more reviews...
                    </p>
                  )}
                </div>
              ) : activePlaceId ? (
                <div className="text-center text-gray-500">
                  No reviews found for this place ID.
                </div>
              ) : (
                <div className="text-center text-gray-500">
                  No reviews loaded yet. Add a business or enter a Place ID to get started.
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {/* Business Confirmation Dialog */}
      <BusinessConfirmationDialog
        business={pendingBusiness}
        onConfirm={handleBusinessConfirmed}
        onCancel={handleBusinessCancelled}
      />
    </div>
  )
}