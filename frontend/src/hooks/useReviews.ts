import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { apiClient } from '@/lib/api'

export interface Review {
  id: string
  place_id: string
  author_name: string
  rating: number
  text: string
  time: string
  sentiment?: string
  sentiment_score?: number
}

export interface ReviewsResponse {
  reviews: Review[]
  total: number
  average_rating: number
}

export function useReviews(placeId?: string) {
  return useQuery({
    queryKey: ['reviews', placeId],
    queryFn: () => apiClient.get(`/api/reviews/place/${placeId}`),
    enabled: !!placeId,
  })
}

export function useFetchReviews() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (placeId: string) => 
      apiClient.post('/api/reviews/fetch', { google_place_id: placeId }),
    onSuccess: (data, placeId) => {
      queryClient.invalidateQueries({ queryKey: ['reviews', placeId] })
      queryClient.invalidateQueries({ queryKey: ['summaries'] })
    },
  })
}