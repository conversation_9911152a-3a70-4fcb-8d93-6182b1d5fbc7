'use client'

import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { businessApi, BusinessInfo, BusinessSearchResponse } from '@/lib/api'

export function useBusinesses() {
  const queryClient = useQueryClient()

  // Get saved businesses
  const {
    data: businesses = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['businesses'],
    queryFn: businessApi.getMyBusinesses,
  })

  // Search business from URL
  const searchFromUrlMutation = useMutation({
    mutationFn: businessApi.searchFromUrl,
  })

  // Save business
  const saveBusinessMutation = useMutation({
    mutationFn: businessApi.saveBusiness,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['businesses'] })
    },
  })

  // Remove business
  const removeBusinessMutation = useMutation({
    mutationFn: businessApi.removeBusiness,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['businesses'] })
    },
  })

  // Fetch reviews by business
  const fetchReviewsMutation = useMutation({
    mutationFn: businessApi.fetchReviewsByBusiness,
  })

  return {
    // Data
    businesses,
    isLoading,
    error,

    // Actions
    searchFromUrl: searchFromUrlMutation.mutateAsync,
    saveBusiness: saveBusinessMutation.mutateAsync,
    removeBusiness: removeBusinessMutation.mutateAsync,
    fetchReviewsByBusiness: fetchReviewsMutation.mutateAsync,
    refetch,

    // Loading states
    isSearching: searchFromUrlMutation.isPending,
    isSaving: saveBusinessMutation.isPending,
    isRemoving: removeBusinessMutation.isPending,
    isFetchingReviews: fetchReviewsMutation.isPending,

    // Errors
    searchError: searchFromUrlMutation.error,
    saveError: saveBusinessMutation.error,
    removeError: removeBusinessMutation.error,
    fetchReviewsError: fetchReviewsMutation.error,
  }
}