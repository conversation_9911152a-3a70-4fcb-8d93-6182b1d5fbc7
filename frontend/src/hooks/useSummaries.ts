import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { apiClient } from '@/lib/api'

export interface Summary {
  id: string
  place_id: string
  summary_text: string
  summary_type: 'weekly' | 'monthly'
  created_at: string
  period_start: string
  period_end: string
}

export function useSummaries(placeId?: string) {
  return useQuery({
    queryKey: ['summaries', placeId],
    queryFn: () => apiClient.get(`/api/summaries?google_place_id=${placeId}`),
    enabled: !!placeId,
  })
}

export function useGenerateSummary() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ placeId, type }: { placeId: string; type: 'weekly' | 'monthly' }) =>
      apiClient.post('/api/summaries/generate', { google_place_id: placeId, summary_type: type }),
    onSuccess: (data, { placeId }) => {
      queryClient.invalidateQueries({ queryKey: ['summaries', placeId] })
    },
  })
}