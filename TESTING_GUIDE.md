# ReviewPulse Testing Guide

## 🚀 Production-Ready Testing Guide

All mock data has been removed. The system now uses:
- **Real Google Places API** for fetching reviews
- **Real AI sentiment analysis** (Gemini/Azure)
- **Full authentication** on all endpoints
- **Complete frontend-backend integration**

## ✅ System Architecture

### Frontend (Next.js + TanStack Query)
- Authentication with Supabase Auth
- Protected routes and dashboard
- Real-time data fetching
- Responsive UI with Tailwind CSS

### Backend (FastAPI + Supabase)
- JWT authentication
- Google Places API integration
- AI sentiment analysis
- Row-level security (RLS)

### Database (Supabase PostgreSQL)
- User management via auth.users
- Reviews with sentiment analysis
- Summaries with AI insights
- Proper foreign key constraints

## 🧪 How to Test

### Prerequisites

#### 1. Environment Variables
Ensure your backend `.env` file has valid API keys:
```bash
# Required for Google Places API
GOOGLE_PLACES_API_KEY=your_google_places_api_key_here

# Required for sentiment analysis (choose one)
GEMINI_API_KEY=your_gemini_api_key_here
# OR
AZURE_TEXT_ANALYTICS_ENDPOINT=your_azure_endpoint
AZURE_TEXT_ANALYTICS_KEY=your_azure_key

# Database (already configured)
SUPABASE_URL=https://evauqytvhvjuryhhfjoy.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 2. Database Setup
Run the database schema:
```bash
cd backend
python setup_database.py
```

## 🚀 Testing Steps

### Step 1: Start Both Services

**Terminal 1 - Backend:**
```bash
cd backend
python main.py
```
Expected output:
```
Starting ReviewPulse API...
INFO:     Application startup complete.
```

**Terminal 2 - Frontend:**
```bash
cd frontend
npm run dev
```

### Step 2: Create User Account

1. Navigate to `http://localhost:3000`
2. You'll be redirected to login page
3. Click "Don't have an account? Sign up"
4. Register with:
   - Email: `<EMAIL>`
   - Password: `password123`
5. Check email for confirmation link and click it
6. Return to login page and sign in

### Step 3: Test Google Places Integration

1. After login, you'll see the dashboard
2. Enter a **real Google Place ID** in the input field

#### Valid Test Place IDs:
- **Google Sydney Office:** `ChIJN1t_tDeuEmsRUsoyG83frY4`
- **Statue of Liberty:** `ChIJKxDbe_lYwokRVf__s8CPn-o`
- **Eiffel Tower:** `ChIJLU7jZClu5kcR4PcOOO6p3I0`
- **McDonald's Times Square:** `ChIJmQJIxlVYwokRLgeuocVOGVU`

3. Click "Fetch Reviews"

### Step 4: Monitor Debug Output

**Backend Console Should Show:**
```
DEBUG: Received request for place_id: ChIJN1t_tDeuEmsRUsoyG83frY4
DEBUG: User: <EMAIL>
DEBUG: Fetched 5 reviews from Google Places API
DEBUG: Got Supabase client
DEBUG: Stored review: John Smith - 5 stars
DEBUG: Stored review: Sarah Johnson - 4 stars
DEBUG: Analyzing sentiment for review: uuid-123
DEBUG: Sentiment: positive
DEBUG: Analyzing sentiment for review: uuid-456
DEBUG: Sentiment: neutral
```

**Frontend Should Show:**
- Loading state: "Fetching..."
- Updated stats cards with real data
- Review list with actual Google reviews
- Sentiment badges (positive/negative/neutral)

### Step 5: Verify Data Flow

1. **Check Stats Cards:**
   - Total Reviews: Should show actual count
   - Average Rating: Should calculate from real ratings
   - Positive Sentiment: Should show percentage based on AI analysis

2. **Check Review List:**
   - Real reviewer names
   - Actual review text
   - Star ratings (1-5)
   - Sentiment badges
   - Dates

3. **Check Database:**
   - Reviews should be stored in Supabase
   - Each review should have sentiment analysis results

### Step 6: Test Multiple Places

1. Try different Place IDs to verify:
   - Each place stores separate reviews
   - User can switch between different businesses
   - Reviews are filtered by user (your reviews only)

## Manual API Testing (Alternative)

#### 1. Register a User
```bash
curl -X POST "http://localhost:8000/api/auth/register" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password123"}'
```

Save the `access_token` from the response.

#### 2. Test Authentication
```bash
TOKEN="your_access_token_here"

curl -X GET "http://localhost:8000/api/auth/me" \
  -H "Authorization: Bearer $TOKEN"
```

#### 3. Test Reviews API
```bash
# Get reviews (empty initially)
curl -X GET "http://localhost:8000/api/reviews/" \
  -H "Authorization: Bearer $TOKEN"

# Get review statistics
curl -X GET "http://localhost:8000/api/reviews/stats" \
  -H "Authorization: Bearer $TOKEN"
```

#### 4. Test Summaries API
```bash
# Get summaries
curl -X GET "http://localhost:8000/api/summaries/" \
  -H "Authorization: Bearer $TOKEN"
```

#### 5. Test Console Logging
```bash
# Test the demo logging endpoint
curl -X GET "http://localhost:8000/test-logging"
```

**Check the server console** - you should see beautifully formatted analysis output like:
```
============================================================
📊 REVIEW SUMMARY ANALYSIS
============================================================
Business: Demo Restaurant
Report Date: August 13, 2025
Recipient: <EMAIL>

📈 REVIEW STATISTICS:
  Total Reviews: 45
  Positive: 28 (62.2%)
  Negative: 12 (26.7%)
  Neutral: 5 (11.1%)

✨ POSITIVE THEMES:
  • Excellent customer service and friendly staff
  • Great food quality and fresh ingredients
  • Clean and welcoming atmosphere

⚠️ NEGATIVE THEMES:
  • Long wait times during peak hours
  • Limited parking availability
  • Some dishes are overpriced

💡 RECOMMENDED IMPROVEMENTS:
  • Consider hiring additional staff during busy periods
  • Explore partnership with nearby parking facilities
  • Review pricing strategy for premium dishes
============================================================
```

### Option 3: Interactive API Documentation
Visit `http://localhost:8000/docs` for interactive Swagger UI to test all endpoints.

## 🔍 Testing Database Setup

### Check Supabase Connection
```bash
python3 test_supabase.py
```

This verifies:
- Supabase connection with both anon and service keys
- All required tables exist
- Database schema is properly applied

## 🎯 Core Milestones Status

| Milestone | Status | Notes |
|-----------|--------|-------|
| Authentication | ✅ Complete | JWT-based auth with Supabase integration |
| Database Schema | ✅ Complete | All tables created with proper RLS |
| Reviews API | ✅ Complete | CRUD operations with filtering |
| Sentiment Analysis | ✅ Complete | Azure + Gemini integration ready |
| AI Summarization | ✅ Complete | Theme extraction and recommendations |
| Console Logging | ✅ Complete | Beautiful formatted output instead of email |
| API Documentation | ✅ Complete | Swagger UI available |
| Error Handling | ✅ Complete | Proper HTTP status codes and validation |

## 🚀 Next Steps for Full Implementation

### With Real Data Integration:

1. **Google Places API Integration**
   ```bash
   # Test fetching real reviews (requires valid Place ID)
   curl -X POST "http://localhost:8000/api/reviews/fetch" \
     -H "Authorization: Bearer $TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"google_place_id": "REAL_PLACE_ID"}'
   ```

2. **AI Services Testing**
   - Test sentiment analysis with real review text
   - Test AI summarization with actual review collections
   - Verify API key configurations

3. **Full Workflow Testing**
   - Fetch reviews → Analyze sentiment → Generate summary → Console log analysis

## 📊 Current API Endpoints

| Endpoint | Method | Description | Auth Required |
|----------|--------|-------------|---------------|
| `/` | GET | Health check | No |
| `/health` | GET | Health check | No |
| `/docs` | GET | API documentation | No |
| `/test-logging` | GET | Demo console logging | No |
| `/api/auth/register` | POST | User registration | No |
| `/api/auth/login` | POST | User login | No |
| `/api/auth/me` | GET | Current user info | Yes |
| `/api/reviews/` | GET | Get reviews | Yes |
| `/api/reviews/fetch` | POST | Fetch from Google Places | Yes |
| `/api/reviews/stats` | GET | Review statistics | Yes |
| `/api/summaries/` | GET | Get summaries | Yes |
| `/api/summaries/generate` | POST | Generate AI summary | Yes |
| `/api/summaries/latest` | GET | Get latest summary | Yes |
| `/api/summaries/send-report` | POST | Log analysis to console | Yes |

## 🎉 Success Criteria

The system is ready when:
- ✅ All API endpoints respond correctly
- ✅ Authentication works properly
- ✅ Database operations succeed
- ✅ Console logging displays formatted analysis
- ✅ Error handling works as expected
- ✅ API documentation is accessible

## 🐛 Troubleshooting

### Common Issues:
1. **Token errors**: Make sure to include `Bearer ` prefix in Authorization header
2. **Database errors**: Verify Supabase configuration and schema
3. **Server won't start**: Check if port 8000 is available
4. **Console logging not working**: Check server console output, not client response

### Check Server Logs:
The server console shows detailed information including:
- Request/response logs
- Console analysis output
- Error messages and stack traces

---

**🎊 Congratulations! ReviewPulse MVP backend is fully functional and ready for testing!**