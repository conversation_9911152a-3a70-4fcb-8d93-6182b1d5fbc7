========================
CODE SNIPPETS
========================
TITLE: Navigate to Terraform Quickstart Directory
DESCRIPTION: This command changes the current working directory to the quickstart sample location within the cloned Terraform repository.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/terraform/create-vm-instance

LANGUAGE: bash
CODE:
```
cd terraform-docs-samples/compute/quickstart/create_vm
```

----------------------------------------

TITLE: Install Cloud Quotas Client Library for Go
DESCRIPTION: Install the `cloud.google.com/go/cloudquotas` package using `go get`.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/quotas/reference/libraries

LANGUAGE: Go
CODE:
```
go get cloud.google.com/go/cloudquotas
```

----------------------------------------

TITLE: Install Google Cloud SDK Terraform Tools
DESCRIPTION: Installs the `google-cloud-sdk-terraform-tools` component using `apt-get`. This package provides the `gcloud beta terraform vet` command, which is necessary for validating Terraform plans against defined policies.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/terraform/policy-validation/quickstart

LANGUAGE: bash
CODE:
```
sudo apt-get install google-cloud-sdk-terraform-tools
```

----------------------------------------

TITLE: Create Google Cloud API Key
DESCRIPTION: This section provides code examples for creating a new Google Cloud API key. It covers creation using the `gcloud` command-line tool, including an option for binding to a service account, and a programmatic example using the C++ client library.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/authentication/api-keys

LANGUAGE: gcloud
CODE:
```
gcloud services api-keys create \
    --display-name=DISPLAY_NAME
```

LANGUAGE: gcloud
CODE:
```
gcloud beta services api-keys create \
    --display-name=DISPLAY_NAME \
    --service-account=SERVICE_ACCOUNT_EMAIL_ADDRESS
```

LANGUAGE: C++
CODE:
```
#include "google/cloud/apikeys/v2/api_keys_client.h"
#include "google/cloud/location.h"

google::api::apikeys::v2::Key CreateApiKey(
    google::cloud::apikeys_v2::ApiKeysClient client,
    google::cloud::Location location, std::string display_name) {
  google::api::apikeys::v2::CreateKeyRequest request;
  request.set_parent(location.FullName());
  request.mutable_key()->set_display_name(std::move(display_name));
  // As an example, restrict the API key's scope to the Natural Language API.
  request.mutable_key()->mutable_restrictions()->add_api_targets()->set_service(
      "language.googleapis.com");

  // Create the key, blocking on the result.
  auto key = client.CreateKey(request).get();
  if (!key) throw std::move(key.status());
  std::cout << "Successfully created an API key: " << key->name() << "\n";

  // For authenticating with the API key, use the value in `key->key_string()`.

  // The API key's resource name is the value in `key->name()`. Use this to
  // refer to the specific key in a `GetKey()` or `DeleteKey()` RPC.
  return *key;
}
```

----------------------------------------

TITLE: Initialize Google Cloud CLI
DESCRIPTION: This command initializes the gcloud CLI, setting up essential configurations for interacting with Google Cloud services from your local development environment. It guides you through selecting a project and region.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/authentication/api-keys-use

LANGUAGE: Shell
CODE:
```
gcloud init
```

----------------------------------------

TITLE: Create custom builder.Dockerfile
DESCRIPTION: Example Dockerfile to extend the default Buildpacks builder image. This snippet demonstrates how to add a system package (subversion) by switching to root, updating apt-get, installing the package, cleaning up, and then switching back to the 'cnb' user.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/buildpacks/build-run-image

LANGUAGE: Dockerfile
CODE:
```
FROM gcr.io/buildpacks/builder
USER root
RUN apt-get update && apt-get install -y --no-install-recommends \
  subversion && \
  apt-get clean && \
  rm -rf /var/lib/apt/lists/*
USER cnb
```

----------------------------------------

TITLE: Initialize Google Cloud CLI
DESCRIPTION: Run this command to initialize the Google Cloud CLI, which sets up your default project, region, and other configurations, guiding you through the initial setup process.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/quotas/custom-constraints

LANGUAGE: gcloud CLI
CODE:
```
gcloud init
```

----------------------------------------

TITLE: Initialize gcloud CLI for Google Cloud Projects
DESCRIPTION: This command initializes the Google Cloud CLI, guiding you through setting up your default project, region, and other configurations. It's a foundational step for interacting with Google Cloud services from your local environment.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/authentication/api-keys

LANGUAGE: gcloud CLI
CODE:
```
gcloud init
```

----------------------------------------

TITLE: Initialize Google Cloud CLI
DESCRIPTION: This command initializes the Google Cloud CLI, setting up your current project and user configuration. It's a prerequisite for using gcloud commands and should be run after installation.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/authentication/api-keys

LANGUAGE: gcloud
CODE:
```
gcloud init
```

----------------------------------------

TITLE: Create Compute Engine VM Instance with Terraform
DESCRIPTION: Terraform configuration to define a single Compute Engine instance running Debian. It specifies the machine type, zone, boot disk image, and includes a startup script to install Flask and necessary dependencies.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/terraform/get-started-with-terraform

LANGUAGE: Terraform
CODE:
```
# Create a single Compute Engine instance
resource "google_compute_instance" "default" {
  name         = "flask-vm"
  machine_type = "f1-micro"
  zone         = "us-west1-a"
  tags         = ["ssh"]

  boot_disk {
    initialize_params {
      image = "debian-cloud/debian-11"
    }
  }

  # Install Flask
  metadata_startup_script = "sudo apt-get update; sudo apt-get install -yq build-essential python3-pip rsync; pip install flask"

  network_interface {
    subnetwork = google_compute_subnetwork.default.id

    access_config {
      # Include this section to give the VM an external IP address
    }
  }
}
```

----------------------------------------

TITLE: Clone Terraform Samples GitHub Repository
DESCRIPTION: Clones the `terraform-docs-samples` GitHub repository, which contains various Terraform examples and configurations. The `--single-branch` flag ensures only the default branch is cloned.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/terraform/resource-management/store-state

LANGUAGE: Git
CODE:
```
git clone https://github.com/terraform-google-modules/terraform-docs-samples.git --single-branch
```

----------------------------------------

TITLE: Cloud Build Terraform Apply Output (Production)
DESCRIPTION: Example log output from a Cloud Build step after successfully applying Terraform configurations to the production environment. It shows key infrastructure outputs like external IP, firewall rule, instance name, network, and subnet.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/terraform/resource-management/managing-infrastructure-as-code

LANGUAGE: text
CODE:
```
Step #3 - "tf apply": external_ip = EXTERNAL_IP_VALUE
Step #3 - "tf apply": firewall_rule = prod-allow-http
Step #3 - "tf apply": instance_name = prod-apache2-instance
Step #3 - "tf apply": network = prod
Step #3 - "tf apply": subnet = prod-subnet-01
```

----------------------------------------

TITLE: Initialize Terraform Project
DESCRIPTION: Initializes the Terraform working directory, downloading necessary provider plugins and modules required for the defined infrastructure, preparing it for plan generation and application.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/terraform/policy-validation/quickstart

LANGUAGE: bash
CODE:
```
terraform init
```

----------------------------------------

TITLE: Install Cloud Quotas Client Library for PHP
DESCRIPTION: Install the `google/cloud` package using Composer.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/quotas/reference/libraries

LANGUAGE: PHP
CODE:
```
composer require google/cloud
```

----------------------------------------

TITLE: Cloud Build Terraform Apply Output (Development)
DESCRIPTION: Example log output from a Cloud Build step after successfully applying Terraform configurations to the development environment. It shows key infrastructure outputs like external IP, firewall rule, instance name, network, and subnet.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/terraform/resource-management/managing-infrastructure-as-code

LANGUAGE: text
CODE:
```
Step #3 - "tf apply": external_ip = EXTERNAL_IP_VALUE
Step #3 - "tf apply": firewall_rule = dev-allow-http
Step #3 - "tf apply": instance_name = dev-apache2-instance
Step #3 - "tf apply": network = dev
Step #3 - "tf apply": subnet = dev-subnet-01
```

----------------------------------------

TITLE: Initialize Terraform Configuration
DESCRIPTION: Command to initialize the Terraform working directory. This step downloads necessary provider plugins and sets up the '.terraform' directory.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/terraform/get-started-with-terraform

LANGUAGE: Shell
CODE:
```
terraform init
```

----------------------------------------

TITLE: Verify Terraform Installation
DESCRIPTION: Checks if Terraform is installed and available in the environment, displaying its usage information and available commands. This step ensures the Terraform CLI is correctly set up before proceeding with infrastructure management.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/terraform/create-vm-instance

LANGUAGE: bash
CODE:
```
terraform
```

----------------------------------------

TITLE: Install Cloud Quotas Client Library for Ruby
DESCRIPTION: Install the `google-cloud-cloud_quotas` gem using gem.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/quotas/reference/libraries

LANGUAGE: Ruby
CODE:
```
gem install google-cloud-cloud_quotas
```

----------------------------------------

TITLE: Specify Application Entrypoint using Procfile
DESCRIPTION: Illustrates how to define the application's entrypoint command using a `Procfile`, where the `web` target specifies the command to run when the container starts.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/buildpacks/ruby

LANGUAGE: Procfile
CODE:
```
web: ruby main.rb
```

----------------------------------------

TITLE: Example Git Status Output After File Modifications
DESCRIPTION: This is an example of the expected output from `git status` after the `PROJECT_ID` placeholders have been replaced in the Terraform configuration files. It shows the modified files that are not yet staged for commit, indicating successful file updates.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/terraform/resource-management/managing-infrastructure-as-code

LANGUAGE: text
CODE:
```
On branch dev
Your branch is up-to-date with 'origin/dev'.
Changes not staged for commit:
 (use "git add <file>..." to update what will be committed)
 (use "git checkout -- <file>..." to discard changes in working directory)
      modified:   environments/dev/backend.tf
      modified:   environments/dev/terraform.tfvars
      modified:   environments/prod/backend.tf
      modified:   environments/prod/terraform.tfvars
no changes added to commit (use "git add" and/or "git commit -a")
```

----------------------------------------

TITLE: Get Single Quota Information using Java Client
DESCRIPTION: This Java snippet demonstrates how to retrieve a single quota information entry using the `CloudQuotasClient`. It initializes the client, builds a `GetQuotaInfoRequest` with a specific quota name, and makes a synchronous call to get the `QuotaInfo` response. The example highlights client setup and request construction.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/quotas/reference/libraries

LANGUAGE: Java
CODE:
```
/*
 * Copyright 2025 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.google.api.cloudquotas.v1.samples;

import com.google.api.cloudquotas.v1.CloudQuotasClient;
import com.google.api.cloudquotas.v1.GetQuotaInfoRequest;
import com.google.api.cloudquotas.v1.QuotaInfo;
import com.google.api.cloudquotas.v1.QuotaInfoName;

public class SyncGetQuotaInfo {

  public static void main(String[] args) throws Exception {
    syncGetQuotaInfo();
  }

  public static void syncGetQuotaInfo() throws Exception {
    // This snippet has been automatically generated and should be regarded as a code template only.
    // It will require modifications to work:
    // - It may require correct/in-range values for request initialization.
    // - It may require specifying regional endpoints when creating the service client as shown in
    // https://cloud.google.com/java/docs/setup#configure_endpoints_for_the_client_library
    try (CloudQuotasClient cloudQuotasClient = CloudQuotasClient.create()) {
      GetQuotaInfoRequest request =
          GetQuotaInfoRequest.newBuilder()
              .setName(
                  QuotaInfoName.ofProjectLocationServiceQuotaInfoName(
                          "[PROJECT]", "[LOCATION]", "[SERVICE]", "[QUOTA_INFO]")
                      .toString())
              .build();
      QuotaInfo response = cloudQuotasClient.getQuotaInfo(request);
    }
  }
}
```

----------------------------------------

TITLE: Create Terraform Project Directory and Configuration File
DESCRIPTION: Commands to create a new directory for the Terraform project and an empty 'main.tf' file, which will contain the Google Cloud resource definitions.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/terraform/get-started-with-terraform

LANGUAGE: Shell
CODE:
```
mkdir tf-tutorial && cd tf-tutorial
```

LANGUAGE: Shell
CODE:
```
nano main.tf
```

----------------------------------------

TITLE: Install Cloud Quotas Client Library for Python
DESCRIPTION: Install the `google-cloud-quotas` package using pip.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/quotas/reference/libraries

LANGUAGE: Python
CODE:
```
pip install google-cloud-quotas
```

----------------------------------------

TITLE: Example Node.js 22 Base Image URL
DESCRIPTION: An example URL demonstrating the customized path for a Node.js 22 base image using the `google-22` stack, hosted in the `us-central1` region.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/buildpacks/base-images

LANGUAGE: Generic
CODE:
```
us-central1-docker.pkg.dev/serverless-runtimes/google-22/runtimes/nodejs22
```

----------------------------------------

TITLE: Examine Copied IAM Policy Constraint YAML
DESCRIPTION: Displays the content of the copied IAM domain restriction constraint YAML file to the terminal, allowing for a quick review of its configuration and parameters.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/terraform/policy-validation/quickstart

LANGUAGE: bash
CODE:
```
cat policies/constraints/iam_service_accounts_only.yaml
```

----------------------------------------

TITLE: Install gcloud CLI beta component
DESCRIPTION: Installs the beta component for the Google Cloud CLI, which is required to use Cloud Quotas features like `gcloud beta quotas info` and `gcloud beta quotas preferences`. This step ensures access to the latest quota management commands.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/quotas/gcloud-cli-examples

LANGUAGE: bash
CODE:
```
gcloud components install beta
```

----------------------------------------

TITLE: Verify Terraform Installation and Usage
DESCRIPTION: This snippet demonstrates how to verify the Terraform CLI installation by running the `terraform` command and interpreting its output, which lists available commands and their descriptions. This applies to both Cloud Shell and local shell environments.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/terraform/install-configure-terraform

LANGUAGE: Shell
CODE:
```
terraform
```

LANGUAGE: Shell
CODE:
```
Usage: terraform [global options] <subcommand> [args]

The available commands for execution are listed below.
The primary workflow commands are given first, followed by
less common or more advanced commands.

Main commands:
  init          Prepare your working directory for other commands
  validate      Check whether the configuration is valid
  plan          Show changes required by the current configuration
  apply         Create or update infrastructure
  destroy       Destroy previously-created infrastructure
```

----------------------------------------

TITLE: Initialize Google Cloud CLI
DESCRIPTION: This command initializes the Google Cloud CLI, setting up your environment for interacting with Google Cloud services. It guides you through selecting a project and configuring default settings.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/buildpacks/build-application

LANGUAGE: bash
CODE:
```
gcloud init
```

----------------------------------------

TITLE: Install Cloud Quotas Client Library for Node.js
DESCRIPTION: Install the `@google-cloud/cloudquotas` package using npm.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/quotas/reference/libraries

LANGUAGE: Node.js
CODE:
```
npm install @google-cloud/cloudquotas
```

----------------------------------------

TITLE: Run Python Flask Application on VM
DESCRIPTION: This command executes the 'app.py' Python script using the 'python3' interpreter. This starts the Flask development server, which will listen for incoming connections, typically on port 5000.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/terraform/get-started-with-terraform

LANGUAGE: Bash
CODE:
```
python3 app.py
```

----------------------------------------

TITLE: View main.tf Terraform Configuration
DESCRIPTION: Displays the contents of the `main.tf` file, which defines the Terraform resources for the remote backend setup.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/terraform/resource-management/store-state

LANGUAGE: Bash
CODE:
```
cat main.tf
```

----------------------------------------

TITLE: Install Google Cloud CLI Beta Component
DESCRIPTION: This command installs the beta component for the Google Cloud CLI, which is necessary to use certain features like Cloud Quotas.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/quotas/troubleshoot

LANGUAGE: shell
CODE:
```
gcloud components install beta
```

----------------------------------------

TITLE: Example: Create Docker Repository in Artifact Registry
DESCRIPTION: An example demonstrating how to create a Docker repository named 'buildpacks-docker-repo' in the 'us-west2' region with a specific description. This illustrates the usage of the `gcloud artifacts repositories create` command with concrete values.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/buildpacks/build-application

LANGUAGE: bash
CODE:
```
gcloud artifacts repositories create buildpacks-docker-repo --repository-format=docker \\
--location=us-west2 --description="Docker repository"
```

----------------------------------------

TITLE: Copy IAM Policy Constraint Sample
DESCRIPTION: Copies a sample IAM domain restriction constraint YAML file from the policy library's samples directory into the designated policies/constraints directory, making it available for policy enforcement.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/terraform/policy-validation/quickstart

LANGUAGE: bash
CODE:
```
cp samples/iam_service_accounts_only.yaml policies/constraints
```

----------------------------------------

TITLE: Example: Build Application with Cloud Buildpacks
DESCRIPTION: A concrete example demonstrating how to use `gcloud builds submit` to build an application and push the image to a specific Artifact Registry path. This command specifies the target image location directly.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/buildpacks/build-application

LANGUAGE: bash
CODE:
```
gcloud builds submit --pack image=us-west2-docker.pkg.dev/my-project-id/my-buildpacks-docker-repo/app-image
```

----------------------------------------

TITLE: Apply Terraform Configuration to Provision Resources
DESCRIPTION: Applies the Terraform configuration defined in `main.tf` to provision the Google Cloud Storage bucket and create the local backend configuration file.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/terraform/resource-management/store-state

LANGUAGE: Bash
CODE:
```
terraform apply
```

----------------------------------------

TITLE: Get Google Cloud Compute Engine Quota Information for Service-Specific Dimensions
DESCRIPTION: This example demonstrates how to retrieve the `QuotaInfo` resource for a service-specific dimension, such as GPU family, using a GET request to the `compute.googleapis.com` service. It shows how to query for `GPUS-PER-GPU-FAMILY-per-project-region` and provides an example response structure, highlighting how `quotaValue` and `applicableLocations` vary per `gpu_family` key.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/quotas/implement-common-use-cases

LANGUAGE: APIDOC
CODE:
```
GET projects/PROJECT_NUMBER/locations/global/services/compute.googleapis.com/quotaInfos/GPUS-PER-GPU-FAMILY-per-project-region

Example Response:
{
    "name": "projects/PROJECT_NUMBER/locations/global/services/compute.googleapis.com/quotaInfos/GpusPerProjectPerRegion",
    "quotatName": "CPUS-per-project-region",
    "metric": "compute.googleapis.com/gpus_per_gpu_family",
    "isPrecise": true,
    "quotaDisplayName": "GPUs per GPU family",
    "metricDisplayName": "GPUs",
    "dimensions": [
        "region",
        "gpu_family"
    ],
    "dimensionsInfo": [
        {
            "dimensions": {
                "region": "us-central1",
                "gpu_family": "NVIDIA_H200"
            },
            "details": {
                "quotaValue": 30,
                "resetValue": 30
            },
            "applicableLocations": [
                "us-central1"
            ]
        },
        {
            "dimensions": {
                "region": "us-central1"
            },
            "details": {
                "quotaValue": 100,
                "resetValue": 100
            },
            "applicableLocations": [
                "us-central1"
            ]
        },
        {
            "dimensions": {
                "gpu_familly": "NVIDIA_H100"
            },
            "details": {
                "quotaValue": 10
            },
            "applicableLocations": [
                "us-central2",
                "us-west1",
                "us-east1"
            ]
        },
        {
            "dimensions": [],
            "details": {
                "quotaValue": 50,
                "resetValue": 50
            },
            "applicableLocations": [
                "us-central1",
                "us-central2",
                "us-west1",
                "us-east1"
            ]
        }
    ]
}
```

----------------------------------------

TITLE: Example Python Dependencies in requirements.txt
DESCRIPTION: This snippet provides an example of a `requirements.txt` file, which is used by pip to declare application dependencies. Each line specifies a package, optionally with a version.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/buildpacks/python

LANGUAGE: Python
CODE:
```
requests==2.20.0
numpy
```

----------------------------------------

TITLE: Get Google Cloud API Key ID using REST
DESCRIPTION: This REST API example shows how to retrieve the ID of an existing Google Cloud API key. The key ID is required for subsequent operations like updating key restrictions and can be obtained by listing all keys associated with a project.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/authentication/api-keys

LANGUAGE: REST
CODE:
```
curl -X GET \
-H "Authorization: Bearer $(gcloud auth print-access-token)" \
"https://apikeys.googleapis.com/v2/projects/PROJECT_ID/locations/global/keys/"
```

----------------------------------------

TITLE: Install Google Places API Node.js Client Library
DESCRIPTION: This command installs the Google Places API (New) client library for Node.js using npm, making it available for use in your project.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/nodejs/docs/reference/places/latest

LANGUAGE: npm
CODE:
```
npm install @googlemaps/places
```

----------------------------------------

TITLE: Configure Pip Timeout with PIP_DEFAULT_TIMEOUT Environment Variable
DESCRIPTION: This example shows how to use the `PIP_DEFAULT_TIMEOUT` environment variable to set the default timeout for `pip` commands to 60 seconds. This is useful for controlling `pip` behavior during dependency installation.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/buildpacks/python

LANGUAGE: Shell
CODE:
```
PIP_DEFAULT_TIMEOUT=60
```

----------------------------------------

TITLE: Example Encoded Quota Preference ID
DESCRIPTION: A concrete example of a `quotaPreferenceId` following the recommended encoding pattern, illustrating a quota for NVIDIA GPUs in `us-central1` for the `compute` service.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/quotas/api-overview

LANGUAGE: Text
CODE:
```
compute_us-central1_nvidia-200
```

----------------------------------------

TITLE: Clone and Duplicate Google Cloud Policy Library Repository
DESCRIPTION: This snippet provides the Git commands to clone the Google Cloud Policy Library sample repository, navigate into it, reconfigure its origin to a personal repository, and push the initial content. This sets up a personalized copy of the policy library.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/terraform/policy_validation/create_policy_library

LANGUAGE: Git
CODE:
```
git clone https://github.com/GoogleCloudPlatform/policy-library.git
cd policy-library
git remote set-url origin POLICY_LIBRARY_REPO
git push origin main
```

----------------------------------------

TITLE: Initialize gcloud CLI for Python Development
DESCRIPTION: This snippet shows how to initialize the Google Cloud CLI, a crucial first step for using Python samples in a local development environment. It configures the necessary settings for interacting with Google Cloud services.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/authentication/api-keys-use

LANGUAGE: Bash
CODE:
```
gcloud init
```

----------------------------------------

TITLE: Test Flask Application with Curl
DESCRIPTION: This command uses 'curl' to send an HTTP GET request to the Flask application running on the VM at port 5000. It verifies that the application is serving traffic correctly and returns the expected 'Hello Cloud!' message.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/terraform/get-started-with-terraform

LANGUAGE: Bash
CODE:
```
curl http://0.0.0.0:5000
```

----------------------------------------

TITLE: Restrict API Key to iOS Bundle IDs with Java Client Library
DESCRIPTION: This Java example demonstrates how to programmatically restrict a Google Cloud API key to specific iOS bundle IDs using the `google-cloud-apikeys` client library. It initializes the API client, constructs a `Key` object with `IosKeyRestrictions` containing the allowed bundle IDs, and then updates the key using `updateKeyAsync`. Ensure the `google-cloud-apikeys` library is installed and replace `projectId` and `keyId` with your actual values.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/authentication/api-keys

LANGUAGE: Java
CODE:
```
import com.google.api.apikeys.v2.ApiKeysClient;
import com.google.api.apikeys.v2.IosKeyRestrictions;
import com.google.api.apikeys.v2.Key;
import com.google.api.apikeys.v2.Restrictions;
import com.google.api.apikeys.v2.UpdateKeyRequest;
import com.google.protobuf.FieldMask;
import java.io.IOException;
import java.util.Arrays;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

public class RestrictApiKeyIos {

  public static void main(String[] args)
      throws IOException, ExecutionException, InterruptedException, TimeoutException {
    // TODO(Developer): Before running this sample,
    //  1. Replace the variable(s) below.
    String projectId = "GOOGLE_CLOUD_PROJECT_ID";

    // ID of the key to restrict. This ID is auto-created during key creation.
    // This is different from the key string. To obtain the key_id,
    // you can also use the lookup api: client.lookupKey()
    String keyId = "key_id";

    restrictApiKeyIos(projectId, keyId);
  }

  // Restricts an API key. You can restrict usage of an API key to specific iOS apps
  // by providing the bundle ID of each app.
  public static void restrictApiKeyIos(String projectId, String keyId)
      throws IOException, ExecutionException, InterruptedException, TimeoutException {
    // Initialize client that will be used to send requests. This client only needs to be created
    // once, and can be reused for multiple requests. After completing all of your requests, call
    // the `apiKeysClient.close()` method on the client to safely
    // clean up any remaining background resources.
    try (ApiKeysClient apiKeysClient = ApiKeysClient.create()) {

      // Restrict the API key usage by specifying the bundle ID(s)
      // of iOS app(s) that can use the key.
      Restrictions restrictions = Restrictions.newBuilder()
          .setIosKeyRestrictions(IosKeyRestrictions.newBuilder()
              .addAllAllowedBundleIds(Arrays.asList("com.google.gmail", "com.google.drive"))
              .build())
          .build();

      Key key = Key.newBuilder()
          .setName(String.format("projects/%s/locations/global/keys/%s", projectId, keyId))
          // Set the restriction(s).
          // For more information on API key restriction, see:
          // https://cloud.google.com/docs/authentication/api-keys
          .setRestrictions(restrictions)
          .build();

      // Initialize request and set arguments.
      UpdateKeyRequest updateKeyRequest = UpdateKeyRequest.newBuilder()
          .setKey(key)
          .setUpdateMask(FieldMask.newBuilder().addPaths("restrictions").build())
          .build();

      // Make the request and wait for the operation to complete.
      Key result = apiKeysClient.updateKeyAsync(updateKeyRequest).get(3, TimeUnit.MINUTES);

      // For authenticating with the API key, use the value in "result.getKeyString()".
      System.out.printf("Successfully updated the API key: %s", result.getName());
    }
  }
}
```

----------------------------------------

TITLE: List Available Constraint Templates
DESCRIPTION: This command lists the contents of the 'policies/templates' directory within the cloned policy library repository. It helps in identifying the pre-defined constraint templates that can be used or modified.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/terraform/policy_validation/create_policy_library

LANGUAGE: Bash
CODE:
```
ls policies/templates
```

----------------------------------------

TITLE: Authenticate and Analyze Sentiment with Google Cloud Natural Language API Key
DESCRIPTION: This snippet demonstrates how to authenticate to the Google Cloud Natural Language API using an API key and perform sentiment analysis on a sample text. It covers client initialization, sending an analysis request, and printing the results. Ensure the respective client library is installed for each language.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/authentication/api-keys-use

LANGUAGE: Go
CODE:
```
import (
	"context"
	"fmt"
	"io"

	language "cloud.google.com/go/language/apiv1"
	"cloud.google.com/go/language/apiv1/languagepb"
	"google.golang.org/api/option"
)

// authenticateWithAPIKey authenticates with an API key for Google Language
// service.
func authenticateWithAPIKey(w io.Writer, apiKey string) error {
	// apiKey := "api-key-string"

	ctx := context.Background()

	// Initialize the Language Service client and set the API key.
	client, err := language.NewClient(ctx, option.WithAPIKey(apiKey))
	if err != nil {
		return fmt.Errorf("NewClient: %w", err)
	}
	defer client.Close()

	text := "Hello, world!"
	// Make a request to analyze the sentiment of the text.
	res, err := client.AnalyzeSentiment(ctx, &languagepb.AnalyzeSentimentRequest{
		Document: &languagepb.Document{
			Source: &languagepb.Document_Content{
				Content: text,
			},
			Type: languagepb.Document_PLAIN_TEXT,
		},
	})
	if err != nil {
		return fmt.Errorf("AnalyzeSentiment: %w", err)
	}

	fmt.Fprintf(w, "Text: %s\n", text)
	fmt.Fprintf(w, "Sentiment score: %v\n", res.DocumentSentiment.Score)
	fmt.Fprintln(w, "Successfully authenticated using the API key.")

	return nil
}
```

LANGUAGE: Node.js
CODE:
```
const {
  v1: {
    LanguageServiceClient
  },
} = require('@google-cloud/language');

/**
 * Authenticates with an API key for Google Language service.
 *
 * @param {string} apiKey An API Key to use
 */
async function authenticateWithAPIKey(apiKey) {
  const language = new LanguageServiceClient({apiKey});

  // Alternatively:
  // const {GoogleAuth} = require('google-auth-library');
  // const auth = new GoogleAuth({apiKey});
  // const language = new LanguageServiceClient({auth});

  const text = 'Hello, world!';

  const [response] = await language.analyzeSentiment({
    document: {
      content: text,
      type: 'PLAIN_TEXT',
    },
  });

  console.log(`Text: ${text}`);
  console.log(
    `Sentiment: ${response.documentSentiment.score}, ${response.documentSentiment.magnitude}`,
  );
  console.log('Successfully authenticated using the API key');
}

authenticateWithAPIKey();
```

LANGUAGE: Python
CODE:
```
from google.cloud import language_v1

def authenticate_with_api_key(api_key_string: str) -> None:
    """
    Authenticates with an API key for Google Language service.

    TODO(Developer): Replace this variable before running the sample.

    Args:
        api_key_string: The API key to authenticate to the service.
    """

    # Initialize the Language Service client and set the API key
    client = language_v1.LanguageServiceClient(
        client_options={"api_key": api_key_string}
    )

    text = "Hello, world!"
    document = language_v1.Document(
        content=text, type_=language_v1.Document.Type.PLAIN_TEXT
    )

    # Make a request to analyze the sentiment of the text.
    sentiment = client.analyze_sentiment(
        request={"document": document}
    ).document_sentiment

    print(f"Text: {text}")
    print(f"Sentiment: {sentiment.score}, {sentiment.magnitude}")
    print("Successfully authenticated using the API key")
```

----------------------------------------

TITLE: Create Custom Run Image Dockerfile for Google Cloud Buildpacks
DESCRIPTION: This Dockerfile extends the default Google Cloud Buildpacks run image, adding `imagemagick` as an example. It sets the user to root for installation and then reverts to a non-root user for security. This file is used to build a custom run image.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/buildpacks/build-run-image

LANGUAGE: Dockerfile
CODE:
```
FROM gcr.io/buildpacks/gcp/run
USER root
RUN apt-get update && apt-get install -y --no-install-recommends \
  imagemagick && \
  apt-get clean && \
  rm -rf /var/lib/apt/lists/*
USER 33:33
```

----------------------------------------

TITLE: Example project.toml for Google Entrypoint
DESCRIPTION: Provides an example of setting the `GOOGLE_ENTRYPOINT` environment variable in `project.toml`. This variable specifies the command to run the application, such as a Gunicorn server for a Python application, ensuring the correct entry point is used during deployment.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/buildpacks/set-environment-variables

LANGUAGE: toml
CODE:
```
[[build.env]]
    name = "GOOGLE_ENTRYPOINT"
    value = "gunicorn -p :8080 main:app"
```

----------------------------------------

TITLE: Clone Terraform Samples GitHub Repository
DESCRIPTION: This command clones the `terraform-docs-samples` GitHub repository, specifically the single branch, to your local environment.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/terraform/create-vm-instance

LANGUAGE: bash
CODE:
```
git clone https://github.com/terraform-google-modules/terraform-docs-samples.git --single-branch
```

----------------------------------------

TITLE: Clone a forked GitHub repository for GitOps
DESCRIPTION: This command clones the forked GitHub repository to your Cloud Shell environment, replacing YOUR_GITHUB_USERNAME with your actual GitHub username. It then navigates into the cloned directory, preparing your environment for the GitOps workflow.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/terraform/resource-management/managing-infrastructure-as-code

LANGUAGE: Shell
CODE:
```
cd ~
git clone https://github.com/YOUR_GITHUB_USERNAME/solutions-terraform-cloudbuild-gitops.git
cd ~/solutions-terraform-cloudbuild-gitops
```

----------------------------------------

TITLE: Example Request Body for Cloud Translation API
DESCRIPTION: Provides a sample JSON request body for the Cloud Translation API, translating the word 'hello' from English to Spanish. This body is used in conjunction with the quota project example.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/authentication/rest

LANGUAGE: json
CODE:
```
{
  "q": "hello",
  "source": "en",
  "target": "es"
}
```

----------------------------------------

TITLE: Get Quota Preference by Request Object in C#
DESCRIPTION: Retrieves a single QuotaPreference resource by constructing a GetQuotaPreferenceRequest object. Includes both synchronous and asynchronous examples.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/quotas/reference/libraries

LANGUAGE: C#
CODE:
```
        public void GetQuotaPreferenceRequestObject()
        {
            // Snippet: GetQuotaPreference(GetQuotaPreferenceRequest, CallSettings)
            // Create client
            CloudQuotasClient cloudQuotasClient = CloudQuotasClient.Create();
            // Initialize request argument(s)
            GetQuotaPreferenceRequest request = new GetQuotaPreferenceRequest
            {
                QuotaPreferenceName = QuotaPreferenceName.FromProjectLocationQuotaPreference("[PROJECT]", "[LOCATION]", "[QUOTA_PREFERENCE]"),
            };
            // Make the request
            QuotaPreference response = cloudQuotasClient.GetQuotaPreference(request);
        }
```

LANGUAGE: C#
CODE:
```
        public async Task GetQuotaPreferenceRequestObjectAsync()
        {
            // Snippet: GetQuotaPreferenceAsync(GetQuotaPreferenceRequest, CallSettings)
            // Additional: GetQuotaPreferenceAsync(GetQuotaPreferenceRequest, CancellationToken)
            // Create client
            CloudQuotasClient cloudQuotasClient = await CloudQuotasClient.CreateAsync();
            // Initialize request argument(s)
            GetQuotaPreferenceRequest request = new GetQuotaPreferenceRequest
            {
                QuotaPreferenceName = QuotaPreferenceName.FromProjectLocationQuotaPreference("[PROJECT]", "[LOCATION]", "[QUOTA_PREFERENCE]"),
            };
            // Make the request
            QuotaPreference response = await cloudQuotasClient.GetQuotaPreferenceAsync(request);
        }
```

----------------------------------------

TITLE: Get Quota Preference by Resource Name Object in C#
DESCRIPTION: Retrieves a single QuotaPreference resource by constructing a QuotaPreferenceName object. Includes both synchronous and asynchronous examples.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/quotas/reference/libraries

LANGUAGE: C#
CODE:
```
        public void GetQuotaPreferenceResourceNames()
        {
            // Snippet: GetQuotaPreference(QuotaPreferenceName, CallSettings)
            // Create client
            CloudQuotasClient cloudQuotasClient = CloudQuotasClient.Create();
            // Initialize request argument(s)
            QuotaPreferenceName name = QuotaPreferenceName.FromProjectLocationQuotaPreference("[PROJECT]", "[LOCATION]", "[QUOTA_PREFERENCE]");
            // Make the request
            QuotaPreference response = cloudQuotasClient.GetQuotaPreference(name);
        }
```

LANGUAGE: C#
CODE:
```
        public async Task GetQuotaPreferenceAsyncResourceNames()
        {
            // Snippet: GetQuotaPreferenceAsync(QuotaPreferenceName, CallSettings)
            // Additional: GetQuotaPreferenceAsync(QuotaPreferenceName, CancellationToken)
            // Create client
            CloudQuotasClient cloudQuotasClient = await CloudQuotasClient.CreateAsync();
            // Initialize request argument(s)
            QuotaPreferenceName name = QuotaPreferenceName.FromProjectLocationQuotaPreference("[PROJECT]", "[LOCATION]", "[QUOTA_PREFERENCE]");
            // Make the request
            QuotaPreference response = await cloudQuotasClient.GetQuotaPreferenceAsync(name);
        }
```

----------------------------------------

TITLE: Get Quota Preference by String Name in C#
DESCRIPTION: Retrieves a single QuotaPreference resource by providing its resource name as a string. Includes both synchronous and asynchronous examples.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/quotas/reference/libraries

LANGUAGE: C#
CODE:
```
        public void GetQuotaPreference()
        {
            // Snippet: GetQuotaPreference(string, CallSettings)
            // Create client
            CloudQuotasClient cloudQuotasClient = CloudQuotasClient.Create();
            // Initialize request argument(s)
            string name = "projects/[PROJECT]/locations/[LOCATION]/quotaPreferences/[QUOTA_PREFERENCE]";
            // Make the request
            QuotaPreference response = cloudQuotasClient.GetQuotaPreference(name);
        }
```

LANGUAGE: C#
CODE:
```
        public async Task GetQuotaPreferenceAsync()
        {
            // Snippet: GetQuotaPreferenceAsync(string, CallSettings)
            // Additional: GetQuotaPreferenceAsync(string, CancellationToken)
            // Create client
            CloudQuotasClient cloudQuotasClient = await CloudQuotasClient.CreateAsync();
            // Initialize request argument(s)
            string name = "projects/[PROJECT]/locations/[LOCATION]/quotaPreferences/[QUOTA_PREFERENCE]";
            // Make the request
            QuotaPreference response = await cloudQuotasClient.GetQuotaPreferenceAsync(name);
        }
```

----------------------------------------

TITLE: Lookup Google Cloud API Key Name (REST/Curl)
DESCRIPTION: This REST example demonstrates how to use a `curl` command to look up the project ID associated with a Google Cloud API key string via the `lookupKey` method of the API Keys v2 API. It requires an authenticated access token for authorization.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/authentication/api-keys

LANGUAGE: Shell
CODE:
```
curl -X GET \
-H "Authorization: Bearer $(gcloud auth print-access-token)" \
-H "Content-Type: application/json; charset=utf-8" \
"https://apikeys.googleapis.com/v2/keys:lookupKey?keyString=KEY_STRING"
```

----------------------------------------

TITLE: Enable Google Cloud APIs
DESCRIPTION: This command enables the specified Google Cloud APIs (Cloud Build and Compute Engine) for your project. This step is often required before using services that depend on these APIs.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/terraform/resource-management/managing-infrastructure-as-code

LANGUAGE: shell
CODE:
```
gcloud services enable cloudbuild.googleapis.com compute.googleapis.com
```

----------------------------------------

TITLE: Get Google Cloud Project ID in Cloud Shell
DESCRIPTION: This command retrieves the ID of the currently selected Google Cloud project within the Cloud Shell environment. It's used to confirm the active project.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/terraform/resource-management/managing-infrastructure-as-code

LANGUAGE: shell
CODE:
```
gcloud config get-value project
```

----------------------------------------

TITLE: Example Terraform Plan JSON for Resource Change Data
DESCRIPTION: Demonstrates the structure of Terraform plan JSON, specifically focusing on the `resource_changes` key which contains data used by Terraform-based constraints. This example shows a `google_compute_address` resource creation.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/terraform/policy-validation/create-terraform-constraints

LANGUAGE: JSON
CODE:
```
{
  "format_version": "0.2",
  "terraform_version": "1.0.10",
  "resource_changes": [
    {
      "address": "google_compute_address.internal_with_subnet_and_address",
      "mode": "managed",
      "type": "google_compute_address",
      "name": "internal_with_subnet_and_address",
      "provider_name": "registry.terraform.io/hashicorp/google",
      "change": {
        "actions": [
          "create"
        ],
        "before": null,
        "after": {
          "address": "**********",
          "address_type": "INTERNAL",
          "description": null,
          "name": "my-internal-address",
          "network": null,
          "prefix_length": null,
          "region": "us-central1",
          "timeouts": null
        },
        "after_unknown": {
          "creation_timestamp": true,
          "id": true,
          "network_tier": true,
          "project": true,
          "purpose": true,
          "self_link": true,
          "subnetwork": true,
          "users": true
        },
        "before_sensitive": false,
        "after_sensitive": {
          "users": []
        }
      }
    }
  ]
}
```

----------------------------------------

TITLE: Authenticate C# Client Library with API Key
DESCRIPTION: This C# sample demonstrates how to provide an API key to the Google Cloud Natural Language client library. It illustrates the process of building a `LanguageServiceClient` with an API key and performing a basic sentiment analysis operation.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/authentication/api-keys-use

LANGUAGE: C#
CODE:
```
using Google.Cloud.Language.V1;
using System;

public class UseApiKeySample
{
    public void AnalyzeSentiment(string apiKey)
    {
        LanguageServiceClient client = new LanguageServiceClientBuilder
        {
            ApiKey = apiKey
        }.Build();

        string text = "Hello, world!";

        AnalyzeSentimentResponse response = client.AnalyzeSentiment(Document.FromPlainText(text));
        Console.WriteLine($"Text: {text}");
        Sentiment sentiment = response.DocumentSentiment;
        Console.WriteLine($"Sentiment: {sentiment.Score}, {sentiment.Magnitude}");
        Console.WriteLine("Successfully authenticated using the API key");
    }
}
```

----------------------------------------

TITLE: GitHub Repository URL for Terraform Cloud Build GitOps
DESCRIPTION: The base URL for the GitHub repository used in the tutorial, demonstrating a Terraform and Cloud Build GitOps solution. Users should replace YOUR_GITHUB_USERNAME with their actual GitHub username.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/terraform/resource-management/managing-infrastructure-as-code

LANGUAGE: text
CODE:
```
https://github.com/YOUR_GITHUB_USERNAME/solutions-terraform-cloudbuild-gitops
```

----------------------------------------

TITLE: Customize Gunicorn Entrypoint with Procfile
DESCRIPTION: This `Procfile` example demonstrates how to set a custom Gunicorn command as the web entrypoint for a Python application, overriding default settings. It binds Gunicorn to the specified port, uses 1 worker and 8 threads, and sets a 0-second timeout. Requires `gunicorn` as a package dependency in `requirements.txt`.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/buildpacks/python

LANGUAGE: Procfile
CODE:
```
web: gunicorn --bind :$PORT --workers 1 --threads 8 --timeout 0 main:app
```

----------------------------------------

TITLE: example-foundation
DESCRIPTION: Shows how the CFT modules can be composed to build a secure cloud foundation.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/terraform/blueprints/terraform-blueprints

LANGUAGE: APIDOC
CODE:
```
Blueprint/Module: example-foundation
  Categories: End-to-end, Operations
  Description: Shows how the CFT modules can be composed to build a secure cloud foundation
  Repository: https://github.com/terraform-google-modules/terraform-example-foundation
```

----------------------------------------

TITLE: Get Quota Information using Ruby Client Library
DESCRIPTION: This snippet illustrates how to fetch quota information using the Google Cloud Quotas Ruby client library. It creates a client instance, initializes a GetQuotaInfoRequest, and invokes the get_quota_info method. The example is a template and requires modifications for correct request initialization and potentially regional endpoint specification.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/quotas/reference/libraries

LANGUAGE: Ruby
CODE:
```
# frozen_string_literal: true

# Copyright 2024 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Auto-generated by gapic-generator-ruby. DO NOT EDIT!

require "google/cloud/cloud_quotas/v1"

##
# Snippet for the get_quota_info call in the CloudQuotas service
#
# This snippet has been automatically generated and should be regarded as a code
# template only. It will require modifications to work:
# - It may require correct/in-range values for request initialization.
# - It may require specifying regional endpoints when creating the service
# client as shown in https://cloud.google.com/ruby/docs/reference.
#
# This is an auto-generated example demonstrating basic usage of
# Google::Cloud::CloudQuotas::V1::CloudQuotas::Client#get_quota_info.
#
def get_quota_info
  # Create a client object. The client can be reused for multiple calls.
  client = Google::Cloud::CloudQuotas::V1::CloudQuotas::Client.new

  # Create a request. To set request fields, pass in keyword arguments.
  request = Google::Cloud::CloudQuotas::V1::GetQuotaInfoRequest.new

  # Call the get_quota_info method.
  result = client.get_quota_info request

  # The returned object is of type Google::Cloud::CloudQuotas::V1::QuotaInfo.
  p result
end
```

----------------------------------------

TITLE: Install Config Connector CLI using apt-get (alternative)
DESCRIPTION: This command provides an alternative method to install the Config Connector CLI using `sudo apt-get install` for scenarios where the `gcloud` component manager is disabled.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/terraform/resource-management/import

LANGUAGE: Shell
CODE:
```
sudo apt-get install google-cloud-sdk-config-connector
```

----------------------------------------

TITLE: Terraform Import Command Output Example
DESCRIPTION: Example output from a successful `terraform import` operation. It shows the process of importing a Google Compute Instance resource into the Terraform state, confirming that the resource is now managed by Terraform.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/terraform/resource-management/import

LANGUAGE: shell
CODE:
```
module.examples-projects-PROJECT_ID-ComputeInstance-us-central1-a.google_compute_instance.instance_1:
Importing from ID
"projects/PROJECT_ID/zones/us-central1-a/instances/instance-1"...
module.examples-projects-PROJECT_ID-ComputeInstance-us-central1-a.google_compute_instance.instance_1:
Import prepared!
 Prepared google_compute_instance for import
module.examples-projects-PROJECT_ID-ComputeInstance-us-central1-a.google_compute_instance.instance_1:
Refreshing state...
[id=projects/PROJECT_ID/zones/us-central1-a/instances/instance-1]

Import successful!

The resources that were imported are shown above. These resources are now in
your Terraform state and will henceforth be managed by Terraform.
```

----------------------------------------

TITLE: Restrict Google Cloud API Key with Java Client Library
DESCRIPTION: This Java example demonstrates how to programmatically restrict a Google Cloud API key using the `google-cloud-apikeys` client library. It shows how to initialize the client, define API targets with services and methods, and update the key's restrictions.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/authentication/api-keys

LANGUAGE: Java
CODE:
```

import com.google.api.apikeys.v2.ApiKeysClient;
import com.google.api.apikeys.v2.ApiTarget;
import com.google.api.apikeys.v2.Key;
import com.google.api.apikeys.v2.Restrictions;
import com.google.api.apikeys.v2.UpdateKeyRequest;
import com.google.protobuf.FieldMask;
import java.io.IOException;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

public class RestrictApiKeyApi {

  public static void main(String[] args)
      throws IOException, ExecutionException, InterruptedException, TimeoutException {
    // TODO(Developer): Before running this sample,
    //  1. Replace the variable(s) below.
    String projectId = "GOOGLE_CLOUD_PROJECT_ID";

    // ID of the key to restrict. This ID is auto-created during key creation.
    // This is different from the key string. To obtain the key_id,
    // you can also use the lookup api: client.lookupKey()
    String keyId = "key_id";

    restrictApiKeyApi(projectId, keyId);
  }

  // Restricts an API key. Restrictions specify which APIs can be called using the API key.
  public static void restrictApiKeyApi(String projectId, String keyId)
      throws IOException, ExecutionException, InterruptedException, TimeoutException {
    // Initialize client that will be used to send requests. This client only needs to be created
    // once, and can be reused for multiple requests. After completing all of your requests, call
    // the `apiKeysClient.close()` method on the client to safely
    // clean up any remaining background resources.
    try (ApiKeysClient apiKeysClient = ApiKeysClient.create()) {

      // Restrict the API key usage by specifying the target service and methods.
      // The API key can only be used to authenticate the specified methods in the service.
      Restrictions restrictions = Restrictions.newBuilder()
          .addApiTargets(ApiTarget.newBuilder()
              .setService("translate.googleapis.com")
              .addMethods("translate.googleapis.com.TranslateText")
              .build())
          .build();

      Key key = Key.newBuilder()
          .setName(String.format("projects/%s/locations/global/keys/%s", projectId, keyId))
          // Set the restriction(s).
          // For more information on API key restriction, see:
          // https://cloud.google.com/docs/authentication/api-keys
          .setRestrictions(restrictions)
          .build();

      // Initialize request and set arguments.
      UpdateKeyRequest updateKeyRequest = UpdateKeyRequest.newBuilder()
          .setKey(key)
          .setUpdateMask(FieldMask.newBuilder().addPaths("restrictions").build())
          .build();

      // Make the request and wait for the operation to complete.
      Key result = apiKeysClient.updateKeyAsync(updateKeyRequest).get(3, TimeUnit.MINUTES);

      // For authenticating with the API key, use the value in "result.getKeyString()".
      System.out.printf("Successfully updated the API key: %s", result.getName());
    }
  }
}
```

----------------------------------------

TITLE: Display Terraform main.tf File Content
DESCRIPTION: This command displays the content of the `main.tf` file, which defines the Google Cloud resources to be created by Terraform.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/terraform/create-vm-instance

LANGUAGE: bash
CODE:
```
cat main.tf
```

----------------------------------------

TITLE: Create Flask Application File on VM
DESCRIPTION: This command opens the 'nano' text editor to create or edit a file named 'app.py' on the virtual machine, which will contain the Python Flask application code.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/terraform/get-started-with-terraform

LANGUAGE: Bash
CODE:
```
nano app.py
```

----------------------------------------

TITLE: Restrict API Key Usage via Google Cloud Console
DESCRIPTION: Step-by-step guide to restrict an existing Google Cloud API key to specific Android applications directly from the Google Cloud Console, requiring the package name and SHA-1 certificate fingerprint.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/authentication/api-keys

LANGUAGE: APIDOC
CODE:
```
1. In the Google Cloud console, go to the **Credentials** page:
   [Go to Credentials](https://console.cloud.google.com/apis/credentials)
2. Click the name of the API key that you want to restrict.
3. In the **Application restrictions** section, select **Android apps**.
4. For each Android app that you want to add, click **Add an item** and enter
   the package name and SHA-1 certificate fingerprint, then click **Done**.
5. Click **Save** to save your changes and return to the API key list.
```

----------------------------------------

TITLE: Submit Sample Application Build to Cloud Build
DESCRIPTION: These commands submit the source code of various sample applications to Google Cloud Build using the `--pack` flag. This initiates the build process, creating a container image and pushing it to the specified Artifact Registry repository. Placeholders like `LOCATION`, `PROJECT_ID`, and `REPO_NAME` must be replaced.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/buildpacks/build-application

LANGUAGE: Shell
CODE:
```
gcloud builds submit --pack image=LOCATION-docker.pkg.dev/PROJECT_ID/REPO_NAME/sample-go
```

LANGUAGE: Shell
CODE:
```
gcloud builds submit --pack image=LOCATION-docker.pkg.dev/PROJECT_ID/REPO_NAME/sample-java-gradle
```

LANGUAGE: Shell
CODE:
```
gcloud builds submit --pack image=LOCATION-docker.pkg.dev/PROJECT_ID/REPO_NAME/sample-node
```

LANGUAGE: Shell
CODE:
```
gcloud builds submit --pack image=LOCATION-docker.pkg.dev/PROJECT_ID/REPO_NAME/sample-php
```

LANGUAGE: Shell
CODE:
```
gcloud builds submit --pack image=LOCATION-docker.pkg.dev/PROJECT_ID/REPO_NAME/sample-python
```

LANGUAGE: Shell
CODE:
```
gcloud builds submit --pack image=LOCATION-docker.pkg.dev/PROJECT_ID/REPO_NAME/sample-ruby
```

LANGUAGE: Shell
CODE:
```
gcloud builds submit --pack image=LOCATION-docker.pkg.dev/PROJECT_ID/REPO_NAME/sample-dotnet
```

----------------------------------------

TITLE: Create Terraform Project Directory and Configuration File
DESCRIPTION: Instructions to create a new directory for the Terraform project and an empty `main.tf` file where Google Cloud resource configurations will be defined. This is the initial setup step in Cloud Shell.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/terraform/deploy-flask-web-server

LANGUAGE: Shell
CODE:
```
mkdir tf-tutorial && cd tf-tutorial

```

LANGUAGE: Shell
CODE:
```
nano main.tf

```

----------------------------------------

TITLE: Add API Restrictions via Google Cloud Console
DESCRIPTION: This guide outlines the steps to add API restrictions to an API key directly through the Google Cloud Console. This process allows you to specify which Google Cloud APIs the key is permitted to access, enhancing security. Ensure the desired APIs are enabled for your project before applying restrictions.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/authentication/api-keys

LANGUAGE: Console
CODE:
```
1. In the Google Cloud console, go to the Credentials page: [Go to Credentials](https://console.cloud.google.com/apis/credentials)
2. Click the name of the API key that you want to restrict.
3. In the API restrictions section, click Restrict key.
4. Select all APIs that your API key will be used to access.
5. Click Save to save your changes and return to the API key list.
```

----------------------------------------

TITLE: Validate Terraform Configuration Plan
DESCRIPTION: Command to validate the syntax of the Terraform configuration and display a preview of the resources that will be created, changed, or destroyed without actually applying the changes.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/terraform/get-started-with-terraform

LANGUAGE: Shell
CODE:
```
terraform plan
```

----------------------------------------

TITLE: Verify gcloud beta terraform vet Installation
DESCRIPTION: Command to verify that the `gcloud beta terraform vet` command-line tool is correctly installed and accessible by displaying its help output.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/terraform/policy-validation/validate-policies

LANGUAGE: bash
CODE:
```
gcloud beta terraform vet --help
```

----------------------------------------

TITLE: Clone Google Cloud Buildpack Samples Repository
DESCRIPTION: Clones the official Google Cloud Buildpack samples repository from GitHub to your local machine, providing example applications for various languages.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/buildpacks/set-environment-variables

LANGUAGE: shell
CODE:
```
git clone https://github.com/GoogleCloudPlatform/buildpack-samples.git
```

----------------------------------------

TITLE: Generate Terraform Execution Plan
DESCRIPTION: Generates and saves a Terraform execution plan to a file named 'test.tfplan'. This plan outlines the infrastructure changes Terraform will make without actually applying them, allowing for review and validation.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/terraform/policy-validation/quickstart

LANGUAGE: bash
CODE:
```
terraform plan -out=test.tfplan
```

----------------------------------------

TITLE: Navigate to Sample Application Directory
DESCRIPTION: These commands change the current directory to the specific sample application folder within the cloned `buildpack-samples` repository. Each command corresponds to a different programming language sample, preparing the environment for building that specific application.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/buildpacks/build-application

LANGUAGE: Shell
CODE:
```
cd buildpack-samples/sample-go
```

LANGUAGE: Shell
CODE:
```
cd buildpack-samples/sample-java-gradle
```

LANGUAGE: Shell
CODE:
```
cd buildpack-samples/sample-node
```

LANGUAGE: Shell
CODE:
```
cd buildpack-samples/sample-php
```

LANGUAGE: Shell
CODE:
```
cd buildpack-samples/sample-python
```

LANGUAGE: Shell
CODE:
```
cd buildpack-samples/sample-ruby
```

LANGUAGE: Shell
CODE:
```
cd buildpack-samples/sample-dotnet
```

----------------------------------------

TITLE: Example Content for Resource Types File
DESCRIPTION: This snippet shows an example of the content for a `types.txt` file, which can be used to specify multiple resource types for bulk export. Each resource type should be on a new line.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/terraform/resource-management/export

LANGUAGE: text
CODE:
```
ComputeBackendBucket
ComputeBackendService
ComputeForwardingRule
```

----------------------------------------

TITLE: Initialize Google Cloud CLI
DESCRIPTION: Initializes the Google Cloud CLI, guiding you through configuration steps to set up your development environment. This is a foundational step for interacting with Google Cloud services.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/quotas/reference/libraries

LANGUAGE: shell
CODE:
```
gcloud init
```

----------------------------------------

TITLE: Example Terraform Plan Output
DESCRIPTION: This snippet shows a typical output from the 'terraform plan' command, indicating the number of resources to be added, changed, or destroyed. It also includes a note about saving the plan for guaranteed actions.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/enterprise/deploy-foundation-using-terraform-from-console

LANGUAGE: Terraform CLI Output
CODE:
```
...
Plan: 6 to add, 0 to change, 0 to destroy.
Note: You didn't use the -out option to save this plan, so Terraform can't
guarantee to take exactly these actions if you run "terraform apply" now.
```

----------------------------------------

TITLE: Include API Key in REST API Call using Query Parameter
DESCRIPTION: This example shows how to include an API key as a `key` query parameter in a REST API request. While functional, this method is less secure as it exposes the API key directly in the URL and should be avoided when possible.

SOURCE: https://cloud.google.com/nodejs/docs/reference/places/latest/authentication/api-keys-use

LANGUAGE: HTTP
CODE:
```
POST https://language.googleapis.com/v1/documents:analyzeEntities?key=API_KEY
```