# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

**ReviewPulse** is a SaaS MVP for SMEs that aggregates Google Reviews, applies AI-powered sentiment analysis, and generates actionable weekly/monthly summaries. The system focuses on centralized customer feedback analysis with automated email reporting and a web dashboard.

## Tech Stack

| Layer | Technology |
|-------|------------|
| **Frontend** | Next.js + shadcn UI components |
| **Auth** | Supabase Auth (email/password + social logins) |
| **Database** | Supabase PostgreSQL |
| **Backend** | Python + FastAPI |
| **AI Services** | Azure Cognitive Services Text Analytics OR Google Gemini |
| **Reviews API** | Google Places API (primary), Playwright fallback |
| **Email** | Resend API |
| **Hosting** | Vercel (frontend), Render/Azure App Service (backend) |

## Architecture Overview

The system follows a multi-layer architecture:

1. **Frontend (Next.js)** - Dashboard with review feed, sentiment charts, AI summaries, and date filtering
2. **Backend (FastAPI)** - API layer handling Google Places integration, AI sentiment analysis, and summary generation
3. **Database (Supabase)** - Stores users, reviews, sentiment results, and generated summaries
4. **Scheduled Jobs** - Weekly/monthly email report generation via Resend
5. **Authentication** - JWT-based auth through Supabase for secure API access

## Key Data Flow

1. User inputs Google Place ID → Backend fetches reviews via Google Places API
2. Reviews stored in Supabase → Sent to AI service for sentiment classification
3. Sentiment results stored → Batch sent to AI for summary generation
4. Summaries stored → Scheduled emails sent via Resend
5. Frontend displays all data through secure API calls

## Development Commands

*Note: Commands will be added as the codebase is implemented*

## Key Integration Points

- **Google Places API**: Rate limiting required to prevent bans
- **AI Services**: Handle both Azure Cognitive Services and Google Gemini as alternatives
- **Supabase**: JWT token validation for all API routes
- **Resend**: HTML email templates with embedded charts
- **Authentication**: Social logins (Google, Facebook) + email/password

## Performance Requirements

- Handle 5k+ reviews without performance degradation
- GDPR compliance for EU users (minimal data retention)
- Secure JWT-based API authentication
- Rate limiting on external API calls

## User Stories Reference

See `docs/user-stories.md` for detailed functional requirements across 6 core user stories:
1. Business onboarding & authentication
2. Google Reviews aggregation
3. Sentiment analysis
4. AI summarization
5. Email reporting
6. Simple dashboard